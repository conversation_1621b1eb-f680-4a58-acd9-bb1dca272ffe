<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="30" height="30" viewBox="0 0 30 30" fill="none">
<g  clip-path="url(#clip-path-hwNR3Mf_uyLBbgWcMUbn0)">
<g filter="url(#filter_yebPly9tb2f1LyJCyPRvZ)">
<path    style="mix-blend-mode:normal" fill="#C8D5FF"  d="M28.1109 15.937C28.1109 23.6882 21.7991 30 14.048 30C6.29685 30 0 23.6732 0 15.937C0 8.2009 6.31184 1.87406 14.063 1.87406L14.063 15.937L28.1109 15.937Z">
</path>
<path    style="mix-blend-mode:normal" fill="url(#linear_fill_yebPly9tb2f1LyJCyPRvZ_1)"  d="M28.1109 15.937C28.1109 23.6882 21.7991 30 14.048 30C6.29685 30 0 23.6732 0 15.937C0 8.2009 6.31184 1.87406 14.063 1.87406L14.063 15.937L28.1109 15.937Z">
</path>
</g>
<g filter="url(#filter_80bCI-HR7aWFdbAO6W7Cp)">
<path    style="mix-blend-mode:normal" fill="#C8D5FF"  d="M29.985 14.063L15.922 14.063L15.922 0C23.6732 0 29.985 6.31184 29.985 14.063Z">
</path>
<path    style="mix-blend-mode:normal" fill="url(#linear_fill_80bCI-HR7aWFdbAO6W7Cp_1)"  d="M29.985 14.063L15.922 14.063L15.922 0C23.6732 0 29.985 6.31184 29.985 14.063Z">
</path>
</g>
</g>
<defs>
<clipPath id="clip-path-hwNR3Mf_uyLBbgWcMUbn0">
<path d="M0 30L30 30L30 0L0 0L0 30Z" fill="white"/>
</clipPath>
<linearGradient id="linear_fill_yebPly9tb2f1LyJCyPRvZ_1" x1="14.0550537109375" y1="1.874053955078125" x2="18.7396240234375" y2="30.004058837890625" gradientUnits="userSpaceOnUse">
<stop offset="0.2906557619571686" stop-color="#FDFFFC" stop-opacity="0" />
<stop offset="1" stop-color="#FFFFFF"  />
</linearGradient>
<filter id="filter_yebPly9tb2f1LyJCyPRvZ" x="-6.5" y="-4.625946044921875" width="41.1109619140625" height="41.125946044921875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_yebPly9tb2f1LyJCyPRvZ"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_yebPly9tb2f1LyJCyPRvZ"/>
<feOffset dx="0" dy="0"/>
<feGaussianBlur stdDeviation="3.25"/>
<feComposite in2="hardAlpha_yebPly9tb2f1LyJCyPRvZ" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.6823529411764706 0 0 0 0 0.7568627450980392 0 0 0 0 1 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="feFloodId_yebPly9tb2f1LyJCyPRvZ" result="dropShadow_1_yebPly9tb2f1LyJCyPRvZ"/>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_yebPly9tb2f1LyJCyPRvZ" result="shape_yebPly9tb2f1LyJCyPRvZ"/>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_yebPly9tb2f1LyJCyPRvZ" result="shape_yebPly9tb2f1LyJCyPRvZ"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_yebPly9tb2f1LyJCyPRvZ"/>
<feOffset dx="0" dy="0"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha_yebPly9tb2f1LyJCyPRvZ" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_yebPly9tb2f1LyJCyPRvZ" result="innerShadow_0_yebPly9tb2f1LyJCyPRvZ" />
</filter>
<linearGradient id="linear_fill_80bCI-HR7aWFdbAO6W7Cp_1" x1="22.9520263671875" y1="0" x2="25.295166015625" y2="14.05999755859375" gradientUnits="userSpaceOnUse">
<stop offset="0.2906557619571686" stop-color="#FDFFFC" stop-opacity="0" />
<stop offset="1" stop-color="#FFFFFF"  />
</linearGradient>
<filter id="filter_80bCI-HR7aWFdbAO6W7Cp" x="9.4219970703125" y="-6.5" width="27.06298828125" height="27.062957763671875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="feFloodId_80bCI-HR7aWFdbAO6W7Cp"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_80bCI-HR7aWFdbAO6W7Cp"/>
<feOffset dx="0" dy="0"/>
<feGaussianBlur stdDeviation="3.25"/>
<feComposite in2="hardAlpha_80bCI-HR7aWFdbAO6W7Cp" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.6823529411764706 0 0 0 0 0.7568627450980392 0 0 0 0 1 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="feFloodId_80bCI-HR7aWFdbAO6W7Cp" result="dropShadow_1_80bCI-HR7aWFdbAO6W7Cp"/>
<feBlend mode="normal" in="SourceGraphic" in2="dropShadow_1_80bCI-HR7aWFdbAO6W7Cp" result="shape_80bCI-HR7aWFdbAO6W7Cp"/>
<feBlend mode="normal" in="SourceGraphic" in2="feFloodId_80bCI-HR7aWFdbAO6W7Cp" result="shape_80bCI-HR7aWFdbAO6W7Cp"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha_80bCI-HR7aWFdbAO6W7Cp"/>
<feOffset dx="0" dy="0"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha_80bCI-HR7aWFdbAO6W7Cp" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape_80bCI-HR7aWFdbAO6W7Cp" result="innerShadow_0_80bCI-HR7aWFdbAO6W7Cp" />
</filter>
</defs>
</svg>
