import { createRouter, createWebHashHistory } from "vue-router";
// import routes from "~pages";

// 使用简化的路由架构，views/App.vue作为布局容器，子路由为具体页面
const routes = [
  {
    path: "/",
    component: () => import("@/views/App.vue"),
    children: [
      {
        path: "",
        name: "home",
        meta: { title: "首页" },
        component: () => import("@/views/index.vue")
      },
      {
        path: "district/:region?",
        name: "district",
        meta: { title: "区级页面" },
        component: () => import("@/views/district.vue")
      },
      {
        path: "building/:id?",
        name: "building",
        meta: { title: "楼宇页面" },
        component: () => import("@/views/building.vue")
      }
    ]
  }
];

const router = createRouter({
  routes: routes,
  history: createWebHashHistory()
})

router.beforeEach((to, from, next) => {
  window.scrollTo(0, 0);
  next();
  // 在路由全局钩子beforeEach中，根据keepAlive属性，统一设置页面的缓存性
  // 作用是每次进入该组件，就将它缓存
  // let token = localStorage.getItem('token');
  // if (!to.meta.no_require_auth) {
  //     // 判断该路由是否需要登录权限
  //     if (token) {
  //         // 通过vuex state获取当前的token是否存在
  //         next();
  //     } else {
  //         next({
  //             path: "/",
  //         });
  //         // 将跳转的路由path作为参数，登录成功后跳转到该路由
  //     }
  // } else {
  //   next();
  // }
});
router.onError(err =>{
  const pattern =  /Loading chunk chunk-([a-z0-9])+ failed/g;//匹配资源丢失的文件
  const isChunkLoadFailed = err.message.match(pattern);
  if(isChunkLoadFailed){
      location.reload();
      // router.replace(targetPath)//会陷入死循环
  }
})


export default router