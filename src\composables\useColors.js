// src/composables/useColors.js
import { computed, reactive, ref, watch } from 'vue'
import { colors, theme } from '@/utils/colors'

export function useColors() {
  const currentTheme = ref('light')
  
  // 响应式颜色配置
  const colorConfig = reactive({
    ...colors,
    ...theme.light
  })
  
  // 切换主题
  const toggleTheme = () => {
    currentTheme.value = currentTheme.value === 'light' ? 'dark' : 'light'
  }
  
  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    Object.assign(colorConfig, theme[newTheme])
  })
  
  // 获取CSS变量样式对象
  const getCSSVars = computed(() => {
    const cssVars = {}
    Object.keys(colorConfig).forEach(key => {
      cssVars[`--color-${key}`] = colorConfig[key]
    })
    return cssVars
  })
  
  return {
    colors: colorConfig,
    currentTheme,
    toggleTheme,
    getCSSVars
  }
}    