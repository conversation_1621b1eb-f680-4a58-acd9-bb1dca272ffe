<template>
  <!-- 右侧：天气和空气质量信息 -->
  <div class="default-header-right">
    <div class="weather-section">
      <div class="weather-content">
        <div class="weather-main">
          <span class="weather-icon">{{ weatherInfo.icon }}</span>
          <div class="weather-info">
            <div class="weather-desc">{{ weatherInfo.weather }}</div>
            <div class="temperature">{{ weatherInfo.temperature }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="air-quality-section">
      <div class="air-content">
        <div class="air-main">
          <div class="aqi-value" :style="{ color: airQuality.color }">PM2.5·{{ airQuality.level }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 天气信息
const weatherInfo = ref({
  temperature: '24°C',
  weather: '晴',
  humidity: '65%',
  windSpeed: '3级',
  icon: '☀️'
});

// 空气质量信息
const airQuality = ref({
  aqi: 85,
  level: '良',
  pm25: 45,
  pm10: 68,
  color: '#66bb6a'
});

// 模拟天气数据更新
const updateWeatherData = () => {
  const temps = ['22°C', '23°C', '24°C', '25°C', '26°C'];
  const weathers = ['晴', '多云', '阴', '小雨'];
  const icons = ['☀️', '⛅', '☁️', '🌧️'];

  const randomIndex = Math.floor(Math.random() * weathers.length);
  weatherInfo.value = {
    temperature: temps[Math.floor(Math.random() * temps.length)],
    weather: weathers[randomIndex],
    humidity: `${Math.floor(Math.random() * 30) + 50}%`,
    windSpeed: `${Math.floor(Math.random() * 5) + 1}级`,
    icon: icons[randomIndex]
  };

  // 模拟空气质量变化
  const aqi = Math.floor(Math.random() * 100) + 50;
  let level = '良';
  let color = '#66bb6a';

  if (aqi <= 50) {
    level = '优';
    color = '#4caf50';
  } else if (aqi <= 100) {
    level = '良';
    color = '#66bb6a';
  } else if (aqi <= 150) {
    level = '轻度污染';
    color = '#ff9800';
  } else {
    level = '中度污染';
    color = '#f44336';
  }

  airQuality.value = {
    aqi,
    level,
    pm25: Math.floor(Math.random() * 50) + 20,
    pm10: Math.floor(Math.random() * 80) + 40,
    color
  };
};

let weatherInterval = null;

onMounted(() => {
  updateWeatherData();
  weatherInterval = setInterval(updateWeatherData, 30000);
});

onUnmounted(() => {
  if (weatherInterval) {
    clearInterval(weatherInterval);
  }
});
</script>

<style scoped lang="less">
.default-header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 天气信息样式 */
.weather-section {
  margin-right: 10px;
}

.weather-content {
  text-align: center;
  z-index: 10;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.weather-icon {
  font-size: 20px;
}

.weather-info {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: 5px;
}

.temperature {
  color: #4fc3f7;
  font-size: 16px;
  font-weight: 600;
  margin-left: 10px;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.weather-desc {
  color: #e3f2fd;
  font-size: 16px;
  opacity: 0.8;
}

/* 空气质量样式 */
.air-quality-section {
  margin-right: 10px;
}

.air-content {
  text-align: center;
  z-index: 10;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.air-main {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.aqi-value {
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 10px currentColor;
}
</style>
