// src/config/colors.js
export const colors = {
  primary: '#4fc3f7',
  success: '#67C23A',
  warning: '#E6A23C',
  danger: '#F56C6C',
  info: '#909399',
  
  // 中性色
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    100: '#F5F7FA',
    200: '#E4E7ED',
    300: '#DCDFE6',
    400: '#C0C4CC',
    500: '#909399',
    600: '#606266',
    700: '#303133',
    800: '#202124',
    900: '#101112'
  },
   blue:{
    100: '#E3F2FD', // 极浅蓝
    200: '#BBDEFB', // 浅蓝
    300: '#90CAF9', // 淡蓝
    400: '#64B5F6', // 天蓝色
    500: '#42A5F5', // 亮蓝
    600: '#2196F3', // 中蓝
    700: '#1E88E5', // 深蓝
    800: '#1976D2', // 海军蓝
    900: '#1565C0', // 暗蓝
    1000: '#0D47A1' // 极深蓝
  },

  blue_green:{
    100: '#AFFAFA',
    200: '#7DFAFA',
    300: '#52F7F7',
    400: '#25F7F7',
    500: '#09E3E3',
    600: '#08C4C4',
    700: '#09ADAD',
  },
  
  green:{
    "100": "#E6F7EE",
    "200": "#B3E0CC",
    "300": "#80CCAA",
    "400": "#4DC688",
    "500": "#2DB372",
    "600": "#1A995F",
    "700": "#0F7D4A",
    "800": "#0A633A",
    "900": "#064A2A"
  },
  purple:{
    "100": "#F5F0FA",
    "200": "#E8D9F3",
    "300": "#D9C3EB",
    "400": "#C8A8E2",
    "500": "#B68CD8",
    "600": "#A370CC",
    "700": "#8F5BC0",
    "800": "#7A49B3",
    "900": "#643894"
  },
  bar:{
    100: '#55B7EE',
    200: '#09ADAD',
    300: '#3CD495',
    400: '#2B8EF3',
    500: '#3254DD',
  },

  // 饼状图配色
  pie:{
    "100": "#E6F7FF",
    "200": "#B3E0FF",
    "300": "#80C8FF",
    "400": "#4DB0FF",
    "500": "#1A94FF",
    "600": "#0078E6",
    "700": "#0061B3",
    "800": "#99CCFF",
    "900": "#7AB8FF",
    "1000": "#5AA4FF",
    "1100": "#D9B3FF",
    "1200": "#C080FF",
    "1300": "#A64DFF",
    "1400": "#8C1AFF",
    "1500": "#B3E6CC",
    "1600": "#80D9A6",
    "1700": "#4DD280",
    "1800": "#1AC95A",
    "1900": "#FFD9B3",
    "2000": "#FFB366"
  },

  // 自定义业务颜色
  secondary: '#3EAF7C',
  accent: '#FF7D00',
  
  // 状态色
  pending: '#E6A23C',
  approved: '#67C23A',
  rejected: '#F56C6C',
  draft: '#909399'
}

// 主题配置
export const theme = {
  light: {
    primary: colors.primary,
    background: colors.gray[100],
    text: colors.gray[700]
  },
  dark: {
    primary: colors.primary,
    background: colors.gray[800],
    text: colors.gray[200]
  }
}    