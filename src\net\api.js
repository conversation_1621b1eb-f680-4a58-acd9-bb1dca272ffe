import request from './request.js';
import axios from 'axios';

// ==================== 接口定义 ====================

// 获取楼宇信息
export const getBuildingInfo = (params) => {
  return request.post({
    url: '/yaolou/buildingView/buildingInfo',
    data: params
  });
};

// 获取所有区字典
export const getDistrictsInfo = (params) => {
  return request.post({
    url: '/yaolou/district/queryDistricts',
    data: params
  });
};

// 获取所有街道字典
export const getStreetsInfo = (params) => {
  return request.post({
    url: '/yaolou/district/queryStreets',
    data: params
  });
};

//获取星级字典
export const getBuildingLevels = () => {
  return request.get({
    url: '/yaolou/district/getBuildingLevels'
  });
};

// 获取核心数据
export const queryBuildingSummary = (params) => {
  return request.post({
    url: '/yaolou/district/queryBuildingSummary',
    params: params
  });
};

// 查询星级楼宇统计
export const queryBuildingLevel = (params) => {
  return request.post({
    url: '/yaolou/buildingView/buildingLevel',
    params: params
  });
};

// 查询单个楼宇信息
export const querySingleBuildingInfo = (params) => {
  return request.post({
    url: '/yaolou/buildingView/querySingleBuildingInfo',
    params: params
  });
};


// 地图数据接口（保留原有逻辑）
export const getMapData = async (apiUrl) => {
  try {
    const response = await axios({
      url: apiUrl,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      timeout: 10000,
    });

    console.log('地图数据获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('地图数据获取失败:', error);

    // 如果是长沙市地图，返回null让调用方处理降级
    if (apiUrl.includes('430100_full.json')) {
      console.log('长沙市地图数据获取失败，返回null');
      return null;
    }

    throw error;
  }
};