<template>
  <div class="map-container">
    <!-- 地图标题 -->
    <div class="map-title">长沙市地图</div>
    <el-button v-if="currentLevel !== 'city'" @click="backToUpperLevel">返回上一级</el-button>
    <!-- 地图容器 -->
    <div id="csmap" class="map"></div>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { useRouter } from "vue-router";
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import { getMapData } from "@/net/api";
// 调用的已下载的本地数据
import ChangSha from "@/assets/json/cs.json";

const router = useRouter();

// ECharts实例
let chartInstance = null;

// 当前层级: 'city' | 'district'
let currentLevel = ref("city");
// 当前选中的区县
let currentRegion = ref("");
// 定义地图数据变量
const currentMapData = ref(null);

const publicUrl = "/api/maps/";

// 地图数据
const districtData = [
  {
    name: "芙蓉区",
    pin: "furong",
    adcode: 430102,
    value: 120,
    area: 42,
    population: 64.2,
    gdp: 1326.4,
    industry: "金融、商贸、服务业"
  },
  {
    name: "天心区",
    pin: "tianxin",
    adcode: 430103,
    value: 110,
    area: 141,
    population: 65.3,
    gdp: 1212.2,
    industry: "文化旅游、现代服务业"
  },
  {
    name: "岳麓区",
    pin: "yuelu",
    adcode: 430104,
    value: 150,
    area: 558,
    population: 152.6,
    gdp: 1661.8,
    industry: "高新技术、教育科研"
  },
  {
    name: "开福区",
    pin: "kaifu",
    adcode: 430105,
    value: 130,
    area: 188,
    population: 82.7,
    gdp: 1292.4,
    industry: "物流、商贸、文化创意"
  },
  {
    name: "雨花区",
    pin: "yuhua",
    adcode: 430111,
    value: 140,
    area: 304,
    population: 94.9,
    gdp: 1373.8,
    industry: "汽车制造、电子商务"
  },
  {
    name: "望城区",
    pin: "wangcheng",
    adcode: 430112,
    value: 90,
    area: 969,
    population: 62.6,
    gdp: 868.0,
    industry: "食品加工、先进制造"
  },
  {
    name: "长沙县",
    pin: "changshaxian",
    adcode: 430121,
    value: 160,
    area: 1756,
    population: 137.5,
    gdp: 2114.4,
    industry: "工程机械、汽车制造"
  },
  {
    name: "宁乡市",
    pin: "ningxiangshi",
    adcode: 430121,
    value: 160,
    area: 1756,
    population: 137.5,
    gdp: 2114.4,
    industry: "工程机械、汽车制造"
  },
  {
    name: "浏阳市",
    pin: "liuyangshi",
    adcode: 430182,
    value: 160,
    area: 1756,
    population: 137.5,
    gdp: 2114.4,
    industry: "工程机械、汽车制造"
  }
];

// 为每个区县分配不同颜色
const colors = [
  // '#E3F2FD', // 极浅蓝 - 接近白色的蓝
  "#BBDEFB", // 浅蓝 - 柔和的起始色
  "#90CAF9", // 淡蓝 - 明亮的过渡色
  "#64B5F6", // 天蓝色 - 标准天空蓝
  "#42A5F5", // 亮蓝 - 常用的主色调
  "#2196F3", // 中蓝 - 经典蓝色
  "#1E88E5", // 深蓝 - 稍深的蓝色
  "#1976D2", // 海军蓝 - 专业感强
  "#1565C0", // 暗蓝 - 接近靛蓝
  "#0D47A1" // 极深蓝 - 接近黑色的蓝
];

// 区域经纬度坐标（用于涟漪点）
const regionCoordinates = {
  //芙蓉区数据
  佳兆业广场: [112.999634, 28.194831],
  长房国际大厦: [113.025918, 28.184695],
  中国石油长沙大厦: [112.986, 28.186366],
  香泽南湖大厦: [113.010653, 28.203472],
  红橡国际广场: [113.039844, 28.207334],
  通程国际大酒店: [112.997832, 28.18905],
  友阿总部办公大楼: [113.007826, 28.187354],
  宇成朝阳广场: [113.010349, 28.187739],
  长沙国金中心: [113.010349, 28.187739],
  //天心区数据
  华远国际中心: [112.969975, 28.189106],
  汇景发展环球中心: [112.969326, 28.174152],
  御邦国际广场: [112.979877, 28.106168],
  保利国际广场: [112.969719, 28.165343],
  蓝湾国际: [106.770264, 31.848249]
};

// 模拟数据 - 增加涟漪点数据
const mockData = {
  //value为楼宇内企业数量
  芙蓉区: [
    { name: "佳兆业广场", value: 121 },
    { name: "长房国际大厦", value: 234 },
    { name: "中国石油长沙大厦", value: 158 },
    { name: "香泽南湖大厦", value: 93 },
    { name: "红橡国际广场", value: 169 },
    { name: "通程国际大酒店", value: 125 },
    { name: "友阿总部办公大楼", value: 106 },
    { name: "宇成朝阳广场", value: 84 },
    { name: "长沙国金中心", value: 284 }
  ],
  天心区: [
    { name: "华远国际中心", value: 163 },
    { name: "汇景发展环球中心", value: 206 },
    { name: "御邦国际广场", value: 163 },
    { name: "保利国际广场", value: 124 },
    { name: "蓝湾国际", value: 187 }
  ]
  // 其他区的数据...
};

//echarts绘图
const loadMap = async (mapType, adcode) => {
  var apiUrl = "";
  var layoutSize = "170%"; // 增加默认布局尺寸，为200%缩放提供更多空间
  try {
    if (mapType == "长沙市") {
      apiUrl = publicUrl + "430100_full.json";
    } else {
      apiUrl = publicUrl + adcode + ".json";
      layoutSize = "80%"; // 增加区级地图的布局尺寸
    }
    const response = await getMapData(apiUrl);
    currentMapData.value = response;

    echarts.registerMap(mapType, currentMapData.value);
    // 设置地图配置
    const option = {
      geo: [
        // 重影
        {
          type: "map",
          map: mapType,
          zlevel: 0,
          aspectScale: 1,
          zoom: 1.01,
          layoutCenter: ["50%", "49%"],
          layoutSize: layoutSize,
          roam: false,
          silent: true,
          // padding: ['1%', '1%', '1%', '1%'],
          itemStyle: {
            borderWidth: 1,
            borderColor: "rgba(58,149,253,0.6)",
            shadowColor: "rgba(65, 214, 255,0.6)",
            shadowOffsetY: 5,
            shadowOffsetZ: 35,
            shadowBlur: 15,
            areaColor: "rgba(5,21,35,0.1)"
          }
        },
        {
          type: "map",
          map: mapType,
          zlevel: -1,
          aspectScale: 1,
          zoom: 1.02,
          layoutCenter: ["50%", "50%"],
          layoutSize: layoutSize,
          roam: false,
          silent: true,
          itemStyle: {
            borderWidth: 1,
            borderColor: "rgba(58,149,253,0.6)",
            shadowColor: "rgba(65, 214, 255,0.6)",
            shadowOffsetY: 5,
            shadowOffsetZ: 35,
            shadowBlur: 15,
            areaColor: "rgba(5,21,35,0.1)"
          }
        },
        {
          type: "map",
          map: mapType,
          zlevel: -2,
          aspectScale: 1,
          zoom: 1.02,
          layoutCenter: ["50%", "51%"],
          layoutSize: layoutSize,
          roam: false,
          silent: true,
          itemStyle: {
            borderWidth: 1,
            borderColor: "rgba(58,149,253,0.6)",
            shadowColor: "rgba(65, 214, 255,0.6)",
            shadowOffsetY: 5,
            shadowOffsetZ: 35,
            shadowBlur: 15,
            areaColor: "rgba(5,21,35,0.1)"
          }
        }
        // {
        //   type: 'map',
        //   map: mapType,
        //   zlevel: -3,
        //   aspectScale: 1,
        //   zoom: 1.03,
        //   layoutCenter: ['50%', '52%'],
        //   layoutSize: layoutSize,
        //   roam: false,
        //   silent: true,
        //   itemStyle: {
        //     borderWidth: 1,
        //     borderColor: 'rgba(58,149,253,0.4)',
        //     shadowColor: 'rgba(29, 111, 165,1)',
        //     shadowOffsetY: 15,
        //     shadowOffsetZ: 35,
        //     shadowBlur: 10,
        //     areaColor: 'rgba(5,21,35,0.1)',
        //   },
        // },
      ],
      // 提示框
      tooltip: {
        trigger: "item",
        formatter: params => {
          if (mapType === "长沙市") {
            const data = districtData.find(item => item.name === params.name);
            if (!data) return params.name;
            return `
            <div class="tooltip-title">${data.name}</div>
            <div class="tooltip-item">GDP: ${data.gdp} 亿元</div>
            <div class="tooltip-item">人口: ${data.population} 万</div>
            <div class="tooltip-item">面积: ${data.area} km²</div>
            <div class="tooltip-item">主要产业: ${data.industry}</div>
          `;
          } else {
            const data = convertToEffectScatterData(mapType).find(
              item => item.name === params.name
            );
            if (!data) return params.name;
            return `
            <div class="tooltip-title">${data.name}</div>
            <div class="tooltip-item">经纬度: [ ${data.value[0]} , ${data.value[1]} ]</div>
            <div class="tooltip-item">企业数量: ${data.value[2]} 个</div>
          `;
          }
        },
        backgroundColor: "rgba(0, 20, 50, 0.8)",
        borderColor: "#1e88e5",
        borderWidth: 1,
        textStyle: { color: "#fff", fontSize: 14 },
        padding: 10,
        extraCssText: "box-shadow: 0 0 15px rgba(30, 136, 229, 0.5);"
      },
      series:
        mapType === "长沙市"
          ? [
              {
                name: mapType,
                type: "map",
                map: mapType,
                zlevel: 1,
                aspectScale: 1,
                zoom: 1,
                layoutCenter: ["50%", "48%"] /* 向上调整地图位置 */,
                layoutSize: layoutSize,
                roam: false,
                silent: false,
                itemStyle: {
                  // areaColor: {
                  //   type: 'linear',
                  //   x: 1000,
                  //   y: 0,
                  //   x2: 0,
                  //   y2: 0,
                  //   colorStops: [
                  //     {
                  //       offset: 0,
                  //       color: 'rgba(3,27,78,0.75)',
                  //     },
                  //     {
                  //       offset: 1,
                  //       color: 'rgba(58,149,253,0.75)',
                  //     },
                  //   ],
                  //   global: true, // 缺省为 false
                  // },
                  borderColor: "rgba(150,201,255,0.9)",
                  borderWidth: 1.2
                },
                rippleEffect: {
                  ////涟漪特效相关配置
                  scale: 5,
                  brushType: "stroke"
                },
                emphasis: {
                  itemStyle: {
                    show: false,
                    color: "#fff",
                    areaColor: "rgba(0,254,233,0.6)"
                  },
                  label: {
                    show: true,
                    color: "#fff"
                  }
                },
                selected: {
                  areaColor: "#9ecae1", // 选中状态颜色，使用蓝色系
                  borderWidth: 2,
                  borderColor: "#3182bd"
                },
                label: {
                  show: true,
                  color: "#fff",
                  fontSize: 12
                },
                data: districtData.map((item, index) => ({
                  name: item.name,
                  value: item.industry,
                  itemStyle: {
                    areaColor: colors[index]
                  }
                }))
              }
            ]
          : [
              {
                name: "涟漪点",
                type: "effectScatter",
                coordinateSystem: "geo",
                data:
                  currentLevel.value === "district"
                    ? convertToEffectScatterData(mapType)
                    : [],
                emphasis: {
                  label: {
                    show: true,
                    position: "top",
                    color: "#fff",
                    formatter: "{b}"
                  }
                },
                label: {
                  show: false,
                  position: "top",
                  color: "#fff"
                },
                symbol: "circle",
                symbolSize: [20, 10],
                itemStyle: {
                  color: "orange",
                  shadowBlur: 10,
                  shadowColor: "orange"
                },
                effectType: "ripple",
                showEffectOn: "render", //emphasis移入显示动画，render一开始显示动画
                rippleEffect: {
                  scale: 5,
                  brushType: "stroke"
                },
                zlevel: 2 // 层级高于地图
              }
            ]
    };
    chartInstance.setOption(option);
    chartInstance.off("click");
    // 监听地图点击事件
    chartInstance.on("click", params => {
      if (params.componentType === "series" && params.seriesType === "map") {
        const regionName = params.name;
        const areaData = districtData.find(item => item.name === params.name);
        // 如果点击的是城市下的区，跳转到区级页面
        if (currentLevel.value === "city" && areaData) {
          // 跳转到区级页面
          router.push({
            path: `/district/${regionName}`,
            query: { regionName: regionName }
          });
        }
      } else if (
        params.componentType === "series" &&
        params.seriesType === "effectScatter"
      ) {
        // 处理楼宇涟漪点点击事件
        const buildingName = params.name;
        const buildingValue = params.value[2]; // 企业数量

        // 根据楼宇名称获取详细信息
        const buildingInfo = getBuildingDetailByName(buildingName);

        if (buildingInfo) {
          // 跳转到楼宇详情页
          const buildingId = Date.now();
          router.push({
            path: `/building/${buildingId}`,
            query: {
              buildingId: buildingId,
              buildingName: buildingInfo.name,
              district: buildingInfo.district,
              street: buildingInfo.street,
              star: buildingInfo.star,
              address: buildingInfo.address
            }
          });
        }
      }
    });

    // 监听窗口大小变化
    window.addEventListener("resize", handleResize);
  } catch (error) {
    console.error(`加载${mapType}地图失败:`, error);
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 根据楼宇名称获取楼宇详细信息
const getBuildingDetailByName = buildingName => {
  // 楼宇详细信息数据库
  const buildingDatabase = {
    佳兆业广场: {
      name: "佳兆业广场",
      district: "芙蓉区",
      street: "五一路街道",
      star: 7,
      address: "五一大道123号"
    },
    长房国际大厦: {
      name: "长房国际大厦",
      district: "芙蓉区",
      street: "解放路街道",
      star: 7,
      address: "解放东路456号"
    },
    中国石油长沙大厦: {
      name: "中国石油长沙大厦",
      district: "芙蓉区",
      street: "湘湖街道",
      star: 6,
      address: "湘江中路789号"
    },
    香泽南湖大厦: {
      name: "香泽南湖大厦",
      district: "芙蓉区",
      street: "南湖路街道",
      star: 4,
      address: "南湖路66号"
    },
    红橡国际广场: {
      name: "红橡国际广场",
      district: "芙蓉区",
      street: "韶山路街道",
      star: 6,
      address: "韶山北路99号"
    },
    华远国际中心: {
      name: "华远国际中心",
      district: "天心区",
      street: "坡子街街道",
      star: 6,
      address: "湘江中路789号"
    },
    汇景发展环球中心: {
      name: "汇景发展环球中心",
      district: "天心区",
      street: "裕南街街道",
      star: 6,
      address: "芙蓉南路321号"
    },
    保利国际广场: {
      name: "保利国际广场",
      district: "天心区",
      street: "金盆岭街道",
      star: 5,
      address: "湘府中路288号"
    },
    蓝湾国际: {
      name: "蓝湾国际",
      district: "天心区",
      street: "新开铺街道",
      star: 6,
      address: "芙蓉南路518号"
    },
    万达广场: {
      name: "万达广场",
      district: "岳麓区",
      street: "银盆岭街道",
      star: 6,
      address: "金星中路666号"
    },
    梅溪湖国际新城: {
      name: "梅溪湖国际新城",
      district: "岳麓区",
      street: "梅溪湖街道",
      star: 7,
      address: "梅溪湖路888号"
    },
    中南大学科技园: {
      name: "中南大学科技园",
      district: "岳麓区",
      street: "岳麓街道",
      star: 5,
      address: "麓山南路932号"
    }
  };

  return buildingDatabase[buildingName] || null;
};

const backToUpperLevel = () => {
  if (currentLevel.value === "district") {
    currentLevel.value = "city";
    loadMap("长沙市", "410000");
  }
};

// 将模拟数据转换为涟漪点数据格式
const convertToEffectScatterData = mapType => {
  const mapData = mockData[mapType] || [];
  return mapData.map(item => {
    const coord = regionCoordinates[item.name] || [0, 0];
    return {
      name: item.name,
      value: [...coord, item.value] // [经度, 纬度, 值]
    };
  });
};

// 组件挂载后初始化地图
onMounted(() => {
  // 初始化图表
  chartInstance = echarts.init(document.getElementById("csmap"));
  nextTick(() => {
    loadMap("长沙市", "410000");
  });
});

// 组件卸载前清理
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible; /* 允许内容溢出显示 */
}

.map-title {
  position: absolute;
  top: 15px;
  left: 20px;
  z-index: 1000;
  color: #4fc3f7;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.6);
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  backdrop-filter: blur(5px);
  user-select: none;
}

.map {
  width: 100%;
  height: 100%;
  min-width: 100%; /* 确保最小宽度 */
  min-height: 100%; /* 确保最小高度 */
  /* 移除max-width和max-height限制，让地图可以充分放大 */
  /* 移除背景框样式 */
  background-color: transparent;
  transition: all 0.3s ease;
  /* 确保地图内容不会被裁剪 */
  overflow: visible;
  /* 为放大后的地图提供足够的显示空间 */
  transform-origin: center center;
  /* 上移200px */
  /* transform: translateY(-200px); */
  /* margin-bottom: -200px; */
}

/* 为地图缩放优化的额外样式 */
.map-container:hover {
  overflow: visible; /* 鼠标悬停时确保内容可见 */
}

/* 确保地图在各种缩放级别下都能正常显示 */
.map canvas {
  max-width: none !important;
  max-height: none !important;
}
</style>
