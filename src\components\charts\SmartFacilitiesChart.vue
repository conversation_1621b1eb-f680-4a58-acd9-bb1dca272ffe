<template>
  <div class="smart-facilities-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="智能化设施" />

        <div class="facilities-grid">
          <div v-for="(facility, index) in facilitiesData" :key="index" class="facility-item">
            <div class="facility-icon">
              <span class="icon">{{ facility.icon }}</span>
            </div>
            <div class="facility-info">
              <div class="facility-name">{{ facility.name }}</div>
              <div class="facility-stats">
                <div class="coverage-rate">
                  <span class="label">覆盖率</span>
                  <span class="value">{{ facility.coverage }}%</span>
                </div>
              </div>
            </div>
            <div class="building-count">
              <span class="count">{{ facility.buildings }}</span>
              <span class="unit">栋</span>
            </div>
            <div class="facility-progress">
              <div class="progress-circle" :style="{ '--progress': facility.coverage + '%' }">
                <div class="progress-value">{{ facility.coverage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import ChartTitle from '@/components/common/ChartTitle.vue';

const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({})
  }
});

// 智能化设施数据
const facilitiesData = ref([
  { name: "智能安防", icon: "🛡️", coverage: 85, buildings: 42 },
  { name: "智能照明", icon: "💡", coverage: 78, buildings: 38 },
  { name: "智能空调", icon: "❄️", coverage: 92, buildings: 45 },
  { name: "智能电梯", icon: "🏢", coverage: 67, buildings: 33 },
  { name: "智能停车", icon: "🚗", coverage: 73, buildings: 36 },
  { name: "智能门禁", icon: "🔐", coverage: 89, buildings: 44 }
]);
</script>

<style scoped lang="less">
.smart-facilities-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  padding: 15px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.facilities-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  overflow-y: auto;
  padding-right: 5px;
}

.facility-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background: rgba(79, 195, 247, 0.05);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 8px;
  gap: 12px;
  transition: all 0.3s ease;
}

.facility-item:hover {
  background: rgba(79, 195, 247, 0.1);
  border-color: rgba(79, 195, 247, 0.4);
  transform: translateY(-2px);
}

.facility-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4fc3f7, #81d4fa);
  border-radius: 50%;
  flex-shrink: 0;
}

.facility-icon .icon {
  font-size: 20px;
}

.facility-info {
  flex: 1;
  min-width: 0;
}

.facility-name {
  color: #e3f2fd;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
}

.facility-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coverage-rate {
  display: flex;
  align-items: center;
  gap: 4px;
}

.coverage-rate .label {
  color: #81d4fa;
  font-size: 11px;
}

.coverage-rate .value {
  color: #4fc3f7;
  font-size: 12px;
  font-weight: bold;
}

.building-count {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.building-count .count {
  color: #66bb6a;
  font-size: 14px;
  font-weight: bold;
}

.building-count .unit {
  color: #81c784;
  font-size: 10px;
}

.facility-progress {
  flex-shrink: 0;
}

.progress-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: conic-gradient(
    #4fc3f7 0deg,
    #4fc3f7 calc(var(--progress) * 3.6deg),
    rgba(79, 195, 247, 0.2) calc(var(--progress) * 3.6deg),
    rgba(79, 195, 247, 0.2) 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: "";
  position: absolute;
  width: 35px;
  height: 35px;
  background: rgba(0, 20, 50, 0.9);
  border-radius: 50%;
}

.progress-value {
  position: relative;
  z-index: 1;
  color: #4fc3f7;
  font-size: 10px;
  font-weight: bold;
}

/* 滚动条样式 */
.facilities-grid::-webkit-scrollbar {
  width: 4px;
}

.facilities-grid::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 2px;
}

.facilities-grid::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.5);
  border-radius: 2px;
}

.facilities-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 195, 247, 0.7);
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .facility-name {
    font-size: 12px;
  }

  .coverage-rate .label,
  .coverage-rate .value {
    font-size: 10px;
  }

  .building-count .count {
    font-size: 13px;
  }

  .progress-circle {
    width: 40px;
    height: 40px;
  }

  .progress-circle::before {
    width: 30px;
    height: 30px;
  }

  .progress-value {
    font-size: 9px;
  }
}
</style>
