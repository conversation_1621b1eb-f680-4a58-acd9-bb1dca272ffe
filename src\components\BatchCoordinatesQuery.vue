<template>
  <div class="batch-coordinates-query">
    <div class="header-section">
      <h3>批量查询写字楼经纬度</h3>
      <p>支持批量查询多个写字楼的经纬度坐标，单次最多查询1000个建筑</p>
    </div>

    <!-- 输入区域 -->
    <div class="input-section">
      <div class="input-methods">
        <el-tabs v-model="inputMethod" @tab-click="handleTabClick">
          <el-tab-pane label="手动输入" name="manual">
            <el-input
              v-model="manualInput"
              type="textarea"
              :rows="8"
              placeholder="请输入写字楼名称，每行一个，例如：&#10;长沙国金中心&#10;华远国际中心&#10;长沙绿地中心T1栋"
            />
            <div class="input-tips">
              <span>已输入 {{ manualBuildingCount }} 个建筑名称</span>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="文件上传" name="upload">
            <el-upload
              class="upload-demo"
              drag
              :auto-upload="false"
              :on-change="handleFileChange"
              accept=".txt,.csv"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">支持 .txt 和 .csv 文件，每行一个建筑名称</div>
            </el-upload>
            <div v-if="inputMethod === 'upload' && buildingList.length > 0" class="input-tips">
              <span>已上传 {{ buildingList.length }} 个建筑名称</span>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="使用现有数据" name="existing">
            <el-button @click="loadExistingData" type="primary">
              加载项目中的写字楼数据 ({{ existingDataCount }}个)
            </el-button>
            <p class="existing-tip">将加载 addressHooks.js 中的所有写字楼名称</p>
            <div v-if="inputMethod === 'existing' && buildingList.length > 0" class="input-tips">
              <span>已加载 {{ buildingList.length }} 个建筑名称</span>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 查询设置 -->
      <div class="query-settings">
        <div class="setting-item">
          <label>城市：</label>
          <el-select v-model="city" style="width: 120px;">
            <el-option label="长沙" value="长沙" />
            <el-option label="北京" value="北京" />
            <el-option label="上海" value="上海" />
            <el-option label="广州" value="广州" />
            <el-option label="深圳" value="深圳" />
          </el-select>
        </div>
        
        <div class="setting-item">
          <label>批次大小：</label>
          <el-select v-model="batchSize" style="width: 100px;">
            <el-option label="5个" :value="5" />
            <el-option label="10个" :value="10" />
            <el-option label="20个" :value="20" />
          </el-select>
        </div>
        
        <div class="setting-item">
          <label>延迟(ms)：</label>
          <el-input-number 
            v-model="delay" 
            :min="0" 
            :max="2000" 
            :step="100"
            style="width: 120px;"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          @click="startBatchQuery"
          :loading="querying"
          :disabled="!canStartQuery"
        >
          开始批量查询 ({{ currentBuildingCount }}个)
        </el-button>
        
        <el-button @click="clearAll">清空</el-button>
        
        <el-button 
          v-if="queryResults.length > 0"
          @click="exportResults"
          type="success"
        >
          导出结果
        </el-button>
      </div>
    </div>

    <!-- 查询进度 -->
    <div v-if="querying" class="progress-section">
      <h4>查询进度</h4>
      <el-progress 
        :percentage="progressPercentage" 
        :status="progressStatus"
        :stroke-width="20"
      />
      <p class="progress-text">{{ progressText }}</p>
    </div>

    <!-- 查询结果 -->
    <div v-if="queryResults.length > 0" class="results-section">
      <div class="results-header">
        <h4>查询结果</h4>
        <div class="results-stats">
          <span class="stat-item success">成功: {{ successCount }}</span>
          <span class="stat-item failed">失败: {{ failedCount }}</span>
          <span class="stat-item rate">成功率: {{ successRate }}%</span>
        </div>
      </div>

      <!-- 结果表格 -->
      <el-table 
        :data="paginatedResults" 
        style="width: 100%"
        max-height="500"
        :default-sort="{prop: 'success', order: 'descending'}"
      >
        <el-table-column prop="name" label="建筑名称" width="200" />
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.success ? 'success' : 'danger'">
              {{ scope.row.success ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="longitude" label="经度" width="120" />
        <el-table-column prop="latitude" label="纬度" width="120" />
        <el-table-column prop="formatted_address" label="详细地址" min-width="300" />
        <el-table-column prop="message" label="错误信息" width="200">
          <template slot-scope="scope">
            <span v-if="!scope.row.success" class="error-message">
              {{ scope.row.message }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="queryResults.length > pageSize"
        @current-change="handlePageChange"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="queryResults.length"
        layout="total, prev, pager, next"
        style="margin-top: 20px; text-align: center;"
      />
    </div>
  </div>
</template>

<script>
import { getBatchCoordinatesLarge } from '../net/addressHooks.js';
import addressHooksData from '../net/addressHooks.js';

export default {
  name: 'BatchCoordinatesQuery',
  data() {
    return {
      inputMethod: 'manual',
      manualInput: '',
      city: '长沙',
      batchSize: 10,
      delay: 200,
      buildingList: [],
      querying: false,
      queryResults: [],
      currentPage: 1,
      pageSize: 20,
      
      // 进度相关
      progressPercentage: 0,
      progressStatus: '',
      progressText: '',
      
      // 现有数据
      existingDataCount: 0
    };
  },
  computed: {
    manualBuildingCount() {
      return this.manualInput.trim() ? this.manualInput.trim().split('\n').filter(line => line.trim()).length : 0;
    },

    currentBuildingCount() {
      if (this.inputMethod === 'manual') {
        return this.manualBuildingCount;
      } else if (this.inputMethod === 'upload' || this.inputMethod === 'existing') {
        return this.buildingList.length;
      }
      return 0;
    },

    canStartQuery() {
      if (this.inputMethod === 'manual') {
        return this.manualBuildingCount > 0;
      } else if (this.inputMethod === 'upload' || this.inputMethod === 'existing') {
        return this.buildingList.length > 0;
      }
      return false;
    },

    successCount() {
      return this.queryResults.filter(r => r.success).length;
    },

    failedCount() {
      return this.queryResults.filter(r => !r.success).length;
    },

    successRate() {
      return this.queryResults.length > 0 ?
        ((this.successCount / this.queryResults.length) * 100).toFixed(1) : 0;
    },

    paginatedResults() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.queryResults.slice(start, end);
    }
  },
  
  mounted() {
    this.existingDataCount = addressHooksData.length;
  },
  
  methods: {
    handleTabClick(tab) {
      this.buildingList = [];
      this.queryResults = [];
    },
    
    handleFileChange(file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        this.buildingList = lines;
        this.$message.success(`成功读取 ${lines.length} 个建筑名称`);
      };
      reader.readAsText(file.raw);
    },
    
    loadExistingData() {
      this.buildingList = originalAddressData.map(item => item.name);
      this.$message.success(`加载了 ${this.buildingList.length} 个现有建筑数据`);
    },
    
    async startBatchQuery() {
      // 准备建筑列表
      if (this.inputMethod === 'manual') {
        this.buildingList = this.manualInput.trim()
          .split('\n')
          .map(line => line.trim())
          .filter(line => line);
      }
      
      if (this.buildingList.length === 0) {
        this.$message.warning('请先输入要查询的建筑名称');
        return;
      }
      
      this.querying = true;
      this.progressPercentage = 0;
      this.progressStatus = '';
      this.queryResults = [];
      
      try {
        this.progressText = '开始批量查询...';
        
        const result = await getBatchCoordinatesLarge(
          this.buildingList, 
          this.city, 
          this.batchSize, 
          this.delay
        );
        
        if (result.success) {
          this.queryResults = result.results;
          this.progressPercentage = 100;
          this.progressStatus = 'success';
          this.progressText = `查询完成！成功 ${result.success_count} 个，失败 ${result.failed_count} 个`;
          
          this.$message.success(`批量查询完成！成功率：${result.success_rate}`);
        } else {
          this.progressStatus = 'exception';
          this.progressText = '查询失败：' + result.message;
          this.$message.error('批量查询失败：' + result.message);
        }
      } catch (error) {
        this.progressStatus = 'exception';
        this.progressText = '查询出错：' + error.message;
        this.$message.error('查询出错：' + error.message);
      } finally {
        this.querying = false;
      }
    },
    
    clearAll() {
      this.manualInput = '';
      this.buildingList = [];
      this.queryResults = [];
      this.progressPercentage = 0;
      this.currentPage = 1;
    },
    
    exportResults() {
      if (this.queryResults.length === 0) return;
      
      const csvHeader = 'Name,Status,Longitude,Latitude,Address,Error';
      const csvRows = this.queryResults.map(item => {
        const status = item.success ? 'Success' : 'Failed';
        const longitude = item.longitude || '';
        const latitude = item.latitude || '';
        const address = (item.formatted_address || '').replace(/"/g, '""');
        const error = (item.message || '').replace(/"/g, '""');
        
        return `"${item.name}","${status}","${longitude}","${latitude}","${address}","${error}"`;
      });
      
      const csvContent = [csvHeader, ...csvRows].join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `building_coordinates_${new Date().getTime()}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      
      this.$message.success('结果已导出为CSV文件');
    },
    
    handlePageChange(page) {
      this.currentPage = page;
    }
  }
};
</script>

<style scoped>
.batch-coordinates-query {
  padding: 20px;
  background: #f5f5f5;
}

.header-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.input-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.input-tips {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.existing-tip {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.query-settings {
  display: flex;
  gap: 20px;
  margin: 20px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item label {
  font-weight: 500;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.progress-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.progress-text {
  margin-top: 10px;
  text-align: center;
  color: #666;
}

.results-section {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.results-stats {
  display: flex;
  gap: 15px;
}

.stat-item {
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: 500;
}

.stat-item.success {
  background: #f0f9ff;
  color: #059669;
}

.stat-item.failed {
  background: #fef2f2;
  color: #dc2626;
}

.stat-item.rate {
  background: #f3f4f6;
  color: #374151;
}

.error-message {
  color: #dc2626;
  font-size: 12px;
}

h3, h4 {
  color: #333;
  margin-bottom: 10px;
}
</style>
