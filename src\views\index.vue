<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import CsMap from '@/components/map.vue';
// 引入新的组件化图表
import BuildingListTable from '@/components/charts/BuildingListTable.vue';
import StarBuildingBarChart from '@/components/charts/StarBuildingBarChart.vue';
import RentTrendLineChart from '@/components/charts/RentTrendLineChart.vue';
import VacancyRateCircle from '@/components/charts/VacancyRateCircle.vue';
import StatisticsGrid from '@/components/charts/StatisticsGrid.vue';
import EnterpriseRiskWarning from '@/components/charts/EnterpriseRiskWarning.vue';
import StarDistribution3DPie from '@/components/charts/StarDistribution3DPie.vue';
import IndustryDistribution3DPie from '@/components/charts/IndustryDistribution3DPie.vue';
import BuildingSalesChart from '@/components/charts/BuildingSalesChart.vue';
import RentalEnterpriseAnalysis from '@/components/charts/RentalEnterpriseAnalysis.vue';
import BuildingInvestmentTop10 from '@/components/charts/BuildingInvestmentTop10.vue';
import BuildingNewCompaniesTop10 from '@/components/charts/BuildingNewCompaniesTop10.vue';
// 引入API接口
import { getBuildingInfo, getDistrictsInfo, getBuildingLevels, queryBuildingSummary, queryBuildingLevel, getStreetsInfo } from '@/net/api.js';

// 移除大屏缩放逻辑，使用统一的响应式设计

// 选中区域状态
const selectedRegion = ref({
  regionName: '长沙市',
  level: 'city',
  areaData: null
});

// 楼宇信息数据状态
const buildingData = ref(null);
const loading = ref(false);
const error = ref(null);

// 字典数据缓存
const districtsDict = ref([]); // 区域字典
const buildingLevelsDict = ref([]); // 星级字典
const streetsDict = ref([]); // 街道字典

// 处理后的楼宇列表数据
const processedBuildingList = ref([]);

// 核心数据状态
const coreData = ref({
  buildingNums: 0,      // 楼宇数量
  buildingArea: 0,      // 总建筑面积
  companyCounts: 0,     // 入驻企业数量
  investedArea: 0       // 总招商面积
});

// 星级楼宇分布数据状态
const buildingLevelData = ref(null);

// 处理地图区域点击事件
const handleRegionClick = (regionData) => {
  selectedRegion.value = regionData;
};

// 获取字典数据
const fetchDictionaries = async () => {
  try {
    console.log('开始获取字典数据...');

    // 并行获取三个字典
    const [districtsResponse, levelsResponse, streetsResponse] = await Promise.all([
      getDistrictsInfo({}),
      getBuildingLevels(),
      getStreetsInfo({})
    ]);

    // 缓存字典数据
    districtsDict.value = districtsResponse.data || [];
    buildingLevelsDict.value = levelsResponse.data || [];
    streetsDict.value = streetsResponse.data || [];

    console.log('字典数据获取成功:', {
      districts: districtsDict.value,
      levels: buildingLevelsDict.value,
      streets: streetsDict.value
    });

    console.log('街道字典数据详情:', streetsDict.value);

  } catch (err) {
    console.error('获取字典数据失败:', err);
    error.value = '获取字典数据失败: ' + err.message;
  }
};

// 获取核心数据
const fetchCoreData = async () => {
  try {
    const response = await queryBuildingSummary({ cityNo: '0731' });
    if (response.data) {
      coreData.value = {
        buildingNums: response.data.buildingNums || 0,
        buildingArea: response.data.buildingArea || 0,
        companyCounts: response.data.companyCounts || 0,
        investedArea: response.data.investedArea || 0,
        gnp: response.data.gnp || 0,
        gdp: response.data.gdp || 0
      };
    }

  } catch (err) {
    error.value = '获取核心数据失败: ' + err.message;
  }
};

// 获取星级楼宇分布数据
const fetchBuildingLevelData = async () => {
  try {
    const response = await queryBuildingLevel({});
    if (response && response.data) {
      if (response.data.buildingLevelList) {
        if (Array.isArray(response.data.buildingLevelList) && response.data.buildingLevelList.length > 0) {
          // 处理数据
          const processedData = processBuildingLevelData(response.data.buildingLevelList);
          buildingLevelData.value = processedData;
        } else {
        }
      } else {
      }
    } else {
    }
  } catch (err) {
    error.value = '获取星级楼宇分布数据失败: ' + err.message;
  }
};

// 处理星级楼宇分布数据
const processBuildingLevelData = (buildingLevelList) => {
  if (!buildingLevelList || !Array.isArray(buildingLevelList)) {
    return null;
  }

  const processedData = {
    districts: [],
    starLevels: [], // 动态从星级字典获取
    data: {}
  };

  // 收集所有可能的星级名称
  const starLevelSet = new Set();

  buildingLevelList.forEach((item, index) => {
    console.log(`处理第${index + 1}条数据:`, item);

    // 检查数据结构 - 实际格式是 073101:{C:1,D:2}
    if (!item || typeof item !== 'object') {
      console.warn(`第${index + 1}条数据格式错误:`, item);
      return;
    }

    // 遍历item的每个属性，属性名是区编码，属性值是星级数据
    Object.keys(item).forEach(districtCode => {
      const starData = item[districtCode];

      console.log(`区编码: ${districtCode}, 星级数据:`, starData);

    // 检查星级数据是否存在
    if (!starData || typeof starData !== 'object') {
      return;
    }

    // 匹配区字典获取区名称
    const districtInfo = districtsDict.value.find(district =>
      district.districtNo === districtCode
    );
    const districtName = districtInfo ? districtInfo.districtName : `未知区域(${districtCode})`;
    // 添加到区域列表
    if (!processedData.districts.includes(districtName)) {
      processedData.districts.push(districtName);
    }

    // 处理星级数据
    const districtStarData = {};
    try {
      Object.keys(starData).forEach(starCode => {
        // 匹配星级字典获取星级名称
        const levelInfo = buildingLevelsDict.value.find(level =>
          level.code === starCode
        );
        const starName = levelInfo ? levelInfo.description : `${starCode}星级`;

        // 使用完整的星级名称作为key，而不是编码
        districtStarData[starName] = starData[starCode];

        // 收集星级名称到Set中
        starLevelSet.add(starName);

        console.log(`星级编码${starCode} -> 星级名称${starName}, 数量: ${starData[starCode]}`);
      });
    } catch (error) {
      return;
    }

      processedData.data[districtName] = districtStarData;
      console.log(`${districtName}的星级数据:`, districtStarData);
    });
  });

  // 将收集到的星级名称转换为排序后的数组
  processedData.starLevels = Array.from(starLevelSet).sort((a, b) => {
    // 按星级数字排序（提取星级名称中的数字）
    const getStarNumber = (starName) => {
      const match = starName.match(/(\d+)/);
      return match ? parseInt(match[1]) : 0;
    };
    return getStarNumber(a) - getStarNumber(b);
  });

  console.log('处理后的星级楼宇分布数据:', processedData);
  console.log('动态获取的星级列表:', processedData.starLevels);
  return processedData;
};

// 数据处理函数：匹配字典数据
const processBuildingData = (buildingDataList) => {
  if (!buildingDataList || !Array.isArray(buildingDataList)) {
    return [];
  }
  return buildingDataList.map(item => {
    const basicVO = item.basicVO;
    const activeVO = item.activeVO;

    if (!basicVO) {
      return null;
    }

    // 匹配星级字典
    const levelInfo = buildingLevelsDict.value.find(level =>
      level.code === basicVO.buildingLevel
    );

    // 匹配区域字典
    const districtInfo = districtsDict.value.find(district =>
      district.districtNo === basicVO.districtNo
    );

    // 封装处理后的数据
    const processedData = {
      buildingName: basicVO.buildingName || '未知楼宇',
      buildingLevel: levelInfo ? levelInfo.description : basicVO.buildingLevel || '未知星级',
      buildingLevelCode: basicVO.buildingLevel,
      districtName: districtInfo ? districtInfo.districtName : '未知区域',
      districtNo: basicVO.districtNo,
      address: basicVO.address || '地址未知',
      companies: basicVO.companies || 0,
      // 保留原始数据以备后用
      originalBasicVO: basicVO,
      originalActiveVO: activeVO,
      originalData: item
    };

    return processedData;
  }).filter(item => item !== null); // 过滤掉无效数据
};

// 获取楼宇信息数据
const fetchBuildingInfo = async (params = {}) => {
  try {
    loading.value = true;
    error.value = null;
    const response = await getBuildingInfo(params);
    buildingData.value = response.data;
    // 处理数据并传递给组件
    // 如果response.data直接是数组，使用它；如果是对象包含list，使用list
    const dataList = Array.isArray(response.data) ? response.data : response.data?.list || [];
    processedBuildingList.value = processBuildingData(dataList);
  } catch (err) {
    error.value = err.message || '获取楼宇信息失败';
  } finally {
    loading.value = false;
  }
};

// 移除时间和天气更新函数，现在由头部组件处理

onMounted(async () => {
  // 页面挂载时的初始化逻辑

  try {
    // 1. 先获取字典数据并缓存
    await fetchDictionaries();

    // 2. 获取核心数据
    await fetchCoreData();

    // 3. 获取星级楼宇分布数据
    await fetchBuildingLevelData();

    // 4. 再获取楼宇信息数据
    await fetchBuildingInfo();

  } catch (err) {
    error.value = '页面初始化失败: ' + err.message;
  }
});

onBeforeUnmount(() => {
  // 清理逻辑
});

</script>

<template>
    <!-- <div class="container"> 
     <div class="screen" ref="dataScreenRef">  -->
  <div id="mainBox" class="index-layout">
    <!-- 主要内容区域 -->
    <div class="unified-main-content">
      <!-- 左侧面板 -->
      <div class="unified-panel unified-left-panel">
        <!-- 楼宇列表表格 -->
        <div class="unified-chart-section">
          <BuildingListTable
            :selected-region="selectedRegion"
            :building-list="processedBuildingList"
            :loading="loading"
            :error="error"
          />
        </div>

        <!-- 星级楼宇分布柱状图 -->
        <div class="unified-chart-section">
          <StarBuildingBarChart
            :selected-region="selectedRegion"
            :building-level-data="buildingLevelData"
          />
        </div>

        <!-- 租金趋势折线图 -->
        <div class="unified-chart-section">
          <RentTrendLineChart />
        </div>
      </div>

      <!-- 中央区域 -->
      <div class="unified-panel unified-center-panel">
        <!-- 数据统计区域 -->
        <div class="data-stats-section">
          <div class="stats-container">
            <!-- 空置率圆环 -->
            <!-- <VacancyRateCircle /> -->
            <div style="width: 20px;"></div>
            <!-- 统计数据 -->
            <StatisticsGrid :core-data="coreData" />




          </div>
        </div>

        <!-- 地图区域 -->
        <div class="map-section">
          <CsMap @region-click="handleRegionClick" />
        </div>

        <!-- 星级分布和行业分布图表区域 -->
        <div class="distribution-charts-section">
          <div class="distribution-chart-left">
            <RentalEnterpriseAnalysis />
          </div>
          <div class="distribution-chart-right">
            <IndustryDistribution3DPie />
          </div>
        </div>

        <!-- 底部图表区域 -->
        <!-- <div class="bottom-section">
          <RealTimeChart />
        </div> -->
      </div>

      <!-- 右侧面板 -->
      <div class="unified-panel unified-right-panel">
        <!-- 企业风险预警 -->
        <div class="unified-chart-section">
          <EnterpriseRiskWarning />
        </div>

        <!-- 星级空置率图表 -->
        <div class="unified-chart-section">
          <StarDistribution3DPie />
        </div>

        <!-- 楼宇销售柱状+折线图 -->
        <div class="unified-chart-section">
          <BuildingSalesChart />
        </div>
      </div>
    </div>
  </div>
    <!-- </div>
  </div> -->
</template>

<style scoped lang="less">

/* 移除固定尺寸缩放，使用统一响应式布局 */

#mainBox {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;

  /* 添加渐变遮罩增强视觉效果 */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(6, 30, 93, 0.4) 0%,
      rgba(6, 30, 93, 0.2) 50%,
      rgba(6, 30, 93, 0.4) 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  /* 确保内容在遮罩之上 */
  > * {
    position: relative;
    z-index: 2;
  }
}

/* 移除所有头部相关样式，现在由AppContainer和头部组件处理 */

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  padding: 20px;
  gap: 20px;
  min-height: 0;
}

/* 左侧面板 */
.left-sidebar {
  flex: 0 0 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 右侧面板 */
.right-sidebar {
  flex: 0 0 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 中央区域 */
.center-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  // gap: 20px;
}

/* 图表区域容器 */
.chart-section {
  flex: 1;
  min-height: 0;
}

/* 分布饼图左右布局 */
.distribution-pies {
  display: flex;
  gap: 10px;
}

.pie-half {
  flex: 1;
  min-height: 0;
}

/* 分布图表区域样式 */
.distribution-charts-section {
  display: flex;
  gap: 0.75vw;
  height: 27vh; /* 响应式高度 */
}

.distribution-chart-left,
.distribution-chart-right {
  flex: 1;
  min-height: 0;
  border-radius: 12px;
  overflow: hidden;
}

/* 地图区域 */
.map-section {
  flex: 1;
  min-height: 0;
  margin-bottom: 0.5vw;
}

/* 数据统计区域样式 */
.data-stats-section {
  height: 160px;
  // margin-top: 20px;
  // padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 科技感长方形边框 */
.data-stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  // background: linear-gradient(135deg,
  //   rgba(6, 30, 93, 0.95) 0%,
  //   rgba(0, 50, 100, 0.85) 50%,
  //   rgba(6, 30, 93, 0.95) 100%
  // );
  border-radius: 12px;
  z-index: 1;
}

@keyframes borderGlow {
  0% {
    opacity: 0.7;
    filter: blur(1px);
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    filter: blur(0.5px);
    transform: scale(1.002);
  }
  100% {
    opacity: 1;
    filter: blur(0px);
    transform: scale(1);
  }
}

.stats-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  position: relative;
  z-index: 2;
  padding: 0 30px; /* 长方形边框的标准内边距 */
}



/* 响应式设计 */
@media (max-width: 1920px) {
  .distribution-charts-section {
    gap: 12px;
    height: 280px;
    margin-top: 8px;
  }
}

@media (max-width: 1600px) {
  .distribution-charts-section {
    gap: 10px;
    height: 250px;
    margin-top: 6px;
  }
}

@media (max-width: 1366px) {
  .distribution-charts-section {
    gap: 8px;
    height: 220px;
    margin-top: 5px;
  }
}

@media (max-width: 1024px) {
  .distribution-charts-section {
    flex-direction: column;
    height: auto;
    gap: 10px;
  }

  .distribution-chart-left,
  .distribution-chart-right {
    height: 200px;
  }
}


</style>