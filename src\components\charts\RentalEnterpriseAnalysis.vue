<template>
  <div class="border-box">
    <ChartTitle title="租赁企业分析" />
    <div class="chart-container" ref="chartRef"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";

import ChartTitle from "@/components/common/ChartTitle.vue";
const chartRef = ref(null);
let chartInstance = null;

// 租赁企业数据
const rentalData = ref({
  years: ["2023年", "2024年"],
  enterpriseCount: [747, 704], // 企业数量
  rentalArea: [33.3, 28.0] // 租赁面积（万m²）
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value);

  const option = {
    backgroundColor: "transparent",
    grid: {
      top: "15%",
      left: "8%",
      right: "8%",
      bottom: "15%",
      containLabel: true
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#4fc3f7"
        }
      },
      backgroundColor: "rgba(0, 20, 50, 0.9)",
      borderColor: "#4fc3f7",
      borderWidth: 1,
      textStyle: {
        color: "#ffffff",
        fontSize: 14
      },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          const unit = param.seriesName === "企业数量" ? "家" : "万m²";
          result += `<div style="margin: 2px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; margin-right: 5px;"></span>
            ${param.seriesName}: ${param.value}${unit}
          </div>`;
        });
        return result;
      }
    },
    legend: {
      data: ["企业数量", "租赁面积"],
      top: "5%",
      textStyle: {
        color: "#4fc3f7",
        fontSize: 14
      },
      itemWidth: 12,
      itemHeight: 8
    },
    xAxis: {
      type: "category",
      data: rentalData.value.years,
      axisPointer: {
        type: "shadow"
      },
      axisLine: {
        lineStyle: {
          color: "#4fc3f7",
          width: 1
        }
      },
      axisLabel: {
        color: "#4fc3f7",
        fontSize: 13
      },
      axisTick: {
        lineStyle: {
          color: "#4fc3f7"
        }
      }
    },
    yAxis: [
      {
        type: "value",
        name: "企业数量(家)",
        position: "left",
        nameTextStyle: {
          color: "#4fc3f7",
          fontSize: 12
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#4fc3f7"
          }
        },
        axisLabel: {
          color: "#4fc3f7",
          fontSize: 12,
          formatter: "{value}"
        },
        splitLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.2)",
            type: "dashed"
          }
        }
      },
      {
        type: "value",
        name: "租赁面积(万m²)",
        position: "right",
        nameTextStyle: {
          color: "#81d4fa",
          fontSize: 12
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#81d4fa"
          }
        },
        axisLabel: {
          color: "#81d4fa",
          fontSize: 12,
          formatter: "{value}"
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: "企业数量",
        type: "bar",
        yAxisIndex: 0,
        data: rentalData.value.enterpriseCount,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#4fc3f7" },
            { offset: 0.5, color: "#29b6f6" },
            { offset: 1, color: "#0288d1" }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#81d4fa" },
              { offset: 0.5, color: "#4fc3f7" },
              { offset: 1, color: "#29b6f6" }
            ])
          }
        },
        barWidth: "35%",
        animationDelay: function(idx) {
          return idx * 200;
        }
      },
      {
        name: "租赁面积",
        type: "line",
        yAxisIndex: 1,
        data: rentalData.value.rentalArea,
        lineStyle: {
          color: "#81d4fa",
          width: 3,
          shadowColor: "rgba(129, 212, 250, 0.5)",
          shadowBlur: 10
        },
        itemStyle: {
          color: "#81d4fa",
          borderColor: "#ffffff",
          borderWidth: 2
        },
        symbol: "circle",
        symbolSize: 8,
        emphasis: {
          itemStyle: {
            color: "#ffffff",
            borderColor: "#81d4fa",
            borderWidth: 3,
            shadowColor: "rgba(129, 212, 250, 0.8)",
            shadowBlur: 15
          },
          lineStyle: {
            width: 4
          }
        },
        smooth: true,
        animationDelay: function(idx) {
          return idx * 300 + 500;
        }
      }
    ],
    animationEasing: "cubicOut",
    animationDuration: 1000
  };

  chartInstance.setOption(option);
};

// 响应式处理
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(async () => {
  await nextTick();
  initChart();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped lang="less">
.border-box {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  padding: 10px 20px;
}

.chart-container {
  height: calc(100% - 10px);
  width: 100%;
  padding: 10px;
}
</style>
