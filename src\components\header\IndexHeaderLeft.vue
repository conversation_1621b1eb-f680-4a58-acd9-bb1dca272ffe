<template>
  <!-- 首页左侧：位置和时间信息 -->
  <div class="index-header-left">
    <div class="location-time-section">
      <div class="location-content">
        <div class="location-main">
          <span class="location-icon">📍</span>
          <div class="location-info">
            <div class="location-text">长沙市</div>
            <div class="location-text">岳麓区</div>
          </div>
        </div>
      </div>
    </div>

    <div class="time-section">
      <div class="time-content">
        <div class="time-main">
          <div class="current-time">{{ currentTime.date }}</div>
          <div class="current-time">星期二</div>
          <div class="current-time">{{ currentTime.time }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 当前时间
const currentTime = ref({
  time: '',
  date: ''
});

// 更新时间
const updateTime = () => {
  const now = new Date();
  const timeStr = now.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  const dateStr = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
  
  currentTime.value = {
    time: timeStr,
    date: dateStr
  };
};

let timeInterval = null;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped lang="less">
.index-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.location-time-section, .time-section {
  margin-right: 10px;
}

.location-content, .time-content {
  text-align: center;
  z-index: 10;
  position: relative;
}

.location-main, .time-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-icon {
  font-size: 20px;
}

.location-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2px;
}

.location-text {
  color: #4fc3f7;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.location-detail {
  color: #e3f2fd;
  font-size: 14px;
  margin-left: 10px;
  // opacity: 0.8;
}

.current-time {
  color: #4fc3f7;
  font-size: 17px;
  // font-weight: 600;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.current-date {
  color: #e3f2fd;
  font-size: 12px;
  opacity: 0.8;
  margin-top: 2px;
}
</style>
