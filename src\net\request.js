import axios from 'axios';

// API配置
const API_CONFIG = {
  baseURL: ' http://47.99.178.193:8092/bigScreen',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// 错误消息映射
const ERROR_MESSAGES = {
  400: '请求参数错误',
  401: '未授权访问',
  403: '禁止访问',
  404: '接口不存在',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时'
};

// 创建axios实例
const instance = axios.create(API_CONFIG);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    const { data } = response;

    // 检查业务状态码
    if (data.code === 200) {
      return data;
    } else {
      const errorMsg = data.message || '请求失败';
      console.error('业务错误:', errorMsg);
      throw new Error(errorMsg);
    }
  },
  (error) => {
    const status = error.response?.status;
    const errorMsg = ERROR_MESSAGES[status] || '网络请求失败';

    console.error('请求错误:', {
      status,
      message: errorMsg,
      url: error.config?.url,
      data: error.response?.data
    });

    throw new Error(errorMsg);
  }
);

// 请求封装
const request = {
  get: (config) => instance.get(config.url, { params: config.params }),
  post: (config) => instance.post(config.url, config.data || config.params),
  put: (config) => instance.put(config.url, config.data || config.params),
  delete: (config) => instance.delete(config.url, { params: config.params })
};

export default request;