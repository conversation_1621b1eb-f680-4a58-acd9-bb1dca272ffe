<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="103.53741455078125" height="142.03778076171875" viewBox="0 0 103.53741455078125 142.03778076171875" fill="none">
<g opacity="0.2">
<path d="M21.0374 117.038L2.53741 75.0378L103.537 75.0378L87.0374 117.038L21.0374 117.038Z"  style="mix-blend-mode:normal" fill="url(#linear_fill_y8ECK7XYcghYmbJxmjsf6)" >
</path>
</g>
<g opacity="0.5">
<ellipse cx="53.53741455078125" cy="122.03778076171875" rx="35" ry="8.5"  style="mix-blend-mode:normal" fill="url(#linear_fill_P2rYjUvYE35dy6ADJqjN7)" >
</ellipse>
</g>
<path    style="mix-blend-mode:normal" fill="#2A5192" fill-opacity="0.4" d="M53.5374 142.038C77.8384 142.038 97.5374 137.561 97.5374 132.038L86.5374 118.538C86.5374 123.232 71.7624 127.038 53.5374 127.038C35.3124 127.038 20.5374 123.232 20.5374 118.538L9.53741 132.038C9.53741 137.561 29.2364 142.038 53.5374 142.038Z">
</path>
<path fill-rule="evenodd"  fill="url(#linear_border_eFWHl00shpiaBlWi1ZZej_0)" style="mix-blend-mode:normal" d="M96.5007 132.349Q96.3844 132.846 95.9003 133.384Q95.1456 134.222 93.6064 135.049Q90.3861 136.78 84.4287 138.134Q78.4012 139.504 70.5687 140.257Q62.4414 141.038 53.5374 141.038Q44.6334 141.038 36.5061 140.257Q28.6736 139.504 22.6462 138.134Q16.6887 136.78 13.4684 135.049Q11.9292 134.222 11.1745 133.384Q10.6904 132.846 10.5742 132.349L20.1852 120.553Q20.9484 121.688 22.6107 122.7Q25.2424 124.303 29.9536 125.516Q34.5907 126.711 40.5842 127.364Q46.7695 128.038 53.5374 128.038Q60.3054 128.038 66.4906 127.364Q72.4841 126.711 77.1212 125.516Q81.8324 124.303 84.4642 122.7Q86.1264 121.688 86.8896 120.553L96.5007 132.349ZM97.5374 132.038C97.5374 137.561 77.8384 142.038 53.5374 142.038C29.2364 142.038 9.53741 137.561 9.53741 132.038L19.6991 119.567L20.5374 118.538C20.5374 118.941 20.6467 119.339 20.8581 119.727C23.1044 123.858 36.8799 127.038 53.5374 127.038C70.195 127.038 83.9704 123.858 86.2168 119.727C86.4281 119.339 86.5374 118.941 86.5374 118.538L87.3757 119.567L97.5374 132.038Z">
</path>
<g mask="url(#mask-5U2bq_j6AWZJUFvUT7aG5)">
<g filter="url(#filter_RQICWdcmn3agYRWwgcV9A)">
<ellipse cx="54.03741455078125" cy="139.03778076171875" rx="13.5" ry="9.5"  style="mix-blend-mode:normal" fill="#4F6EA4" >
</ellipse>
</g>
</g>
<ellipse  cx="53.53741455078125" cy="118.03778076171875" rx="33" ry="8.5"   style="mix-blend-mode:normal" fill="url(#linear_fill_Mog6b9n9t8xUBZ5IU6vuY_0)" >
</ellipse>
<ellipse  cx="53.53741455078125" cy="118.03778076171875" rx="33" ry="8.5"   style="mix-blend-mode:normal" fill="url(#linear_fill_Mog6b9n9t8xUBZ5IU6vuY_1)" fill-opacity="0.4699999988079071">
</ellipse>
<ellipse  cx="53.53741455078125" cy="118.03778076171875" rx="33" ry="8.5"   style="mix-blend-mode:normal" fill="url(#linear_fill_Mog6b9n9t8xUBZ5IU6vuY_2)" fill-opacity="0.33000001311302185">
</ellipse>
<path fill-rule="evenodd"  fill="url(#linear_border_Mog6b9n9t8xUBZ5IU6vuY_0)" style="mix-blend-mode:normal" d="M53.5374 126.538C71.7628 126.538 86.5374 122.732 86.5374 118.038C86.5374 113.343 71.7628 109.538 53.5374 109.538C35.312 109.538 20.5374 113.343 20.5374 118.038C20.5374 122.732 35.312 126.538 53.5374 126.538ZM85.084 116.947Q85.5374 117.518 85.5374 118.038Q85.5374 118.558 85.084 119.129Q84.5414 119.812 83.424 120.492Q81.046 121.94 76.6225 123.08Q72.1248 124.238 66.2742 124.876Q60.197 125.538 53.5374 125.538Q46.8779 125.538 40.8006 124.876Q34.95 124.238 30.4523 123.08Q26.0288 121.94 23.6508 120.492Q22.5335 119.812 21.9908 119.129Q21.5374 118.558 21.5374 118.038Q21.5374 117.518 21.9908 116.947Q22.5335 116.264 23.6508 115.583Q26.0288 114.135 30.4523 112.996Q34.95 111.837 40.8006 111.2Q46.8779 110.538 53.5374 110.538Q60.1969 110.538 66.2742 111.2Q72.1248 111.837 76.6225 112.996Q81.046 114.135 83.424 115.583Q84.5414 116.264 85.084 116.947Z">
</path>
<g opacity="0.7">
<g filter="url(#filter_HXvCmk4R_dptnSrT6O0nr)">
<ellipse cx="50.8262939453125" cy="33.72278076171875" transform="rotate(45 43.3262939453125 10.53778076171875)" rx="7.5" ry="23.185"  style="mix-blend-mode:normal" fill="#45C999" >
</ellipse>
</g>
</g>
<g opacity="0.7">
<g filter="url(#filter_ta1VTlurec0zxwrwx9pyA)">
<ellipse cx="94.0313720703125" cy="80.97918701171875" transform="rotate(45 88.1563720703125 68.47918701171875)" rx="5.875" ry="12.5"  style="mix-blend-mode:normal" fill="#13553D" >
</ellipse>
</g>
</g>
<circle  cx="52.5372314453125" cy="60.53778076171875" r="36"   style="mix-blend-mode:normal" fill="#41597F" fill-opacity="0.18">
</circle>
<path fill-rule="evenodd"  fill="url(#linear_border_HMutdm6xSwV4GBfH4Xukg_0)" style="mix-blend-mode:normal" d="M52.5372 96.5378C72.4195 96.5378 88.5372 80.42 88.5372 60.5378C88.5372 40.6555 72.4195 24.5378 52.5372 24.5378C32.655 24.5378 16.5372 40.6555 16.5372 60.5378C16.5372 80.42 32.655 96.5378 52.5372 96.5378ZM52.5372 25.5378C71.8672 25.5378 87.5372 41.2078 87.5372 60.5378C87.5372 79.8677 71.8672 95.5378 52.5372 95.5378C33.2073 95.5378 17.5372 79.8677 17.5372 60.5378C17.5372 41.2078 33.2073 25.5378 52.5372 25.5378Z">
</path>
<path fill-rule="evenodd"  fill="url(#linear_border_HMutdm6xSwV4GBfH4Xukg_1)" style="mix-blend-mode:normal" d="M52.5372 96.5378C72.4195 96.5378 88.5372 80.42 88.5372 60.5378C88.5372 40.6555 72.4195 24.5378 52.5372 24.5378C32.655 24.5378 16.5372 40.6555 16.5372 60.5378C16.5372 80.42 32.655 96.5378 52.5372 96.5378ZM52.5372 25.5378C71.8672 25.5378 87.5372 41.2078 87.5372 60.5378C87.5372 79.8677 71.8672 95.5378 52.5372 95.5378C33.2073 95.5378 17.5372 79.8677 17.5372 60.5378C17.5372 41.2078 33.2073 25.5378 52.5372 25.5378Z">
</path>
<g mask="url(#mask-p4WSRw47H6JYUwF74nrdk)" opacity="0.48">
<g filter="url(#filter_Nh0m_lM6pwZXS2lihwTAb)">
<circle cx="48.5372314453125" cy="56.53778076171875" r="30"  style="mix-blend-mode:normal" fill="#1B4852" >
</circle>
</g>
</g>
<g mask="url(#mask-p4WSRw47H6JYUwF74nrdk)" opacity="0.3">
<g filter="url(#filter_43FcvB_ZQ8gthunlZmC9_)">
<ellipse cx="97.1014990234375" cy="81.73101684570312" transform="rotate(44.38477474590305 85.4114990234375 57.761016845703125)" rx="11.69" ry="23.97"  style="mix-blend-mode:normal" fill="#497DB1" >
</ellipse>
</g>
</g>
<g mask="url(#mask-p4WSRw47H6JYUwF74nrdk)" opacity="0.7">
<g filter="url(#filter_scLYxPcnWkFzFg7E9jyep)">
<ellipse cx="88.7295361328125" cy="83.03778076171875" transform="rotate(44.38477474590305 83.0245361328125 70.53778076171875)" rx="5.705" ry="12.5"  style="mix-blend-mode:normal" fill="#0A3764" >
</ellipse>
</g>
</g>
<g mask="url(#mask-p4WSRw47H6JYUwF74nrdk)">
<g filter="url(#filter_ztRfTOGTjAUQ1o1xvP8vG)">
<ellipse cx="88.8345361328125" cy="86.03778076171875" transform="rotate(44.38477474590305 86.0245361328125 73.53778076171875)" rx="2.81" ry="12.5"  style="mix-blend-mode:normal" fill="#45C999" >
</ellipse>
</g>
</g>
<g opacity="0.6">
<g filter="url(#filter_qeIsm43CTfQOKSTSLVTaz)">
<path d="M16.5372 60.5378C16.5372 40.6555 32.655 24.5378 52.5372 24.5378C54.0372 39.0378 48.9372 66.5378 16.5372 60.5378Z"  style="mix-blend-mode:normal" fill="url(#linear_fill_qeIsm43CTfQOKSTSLVTaz)" >
</path>
</g>
</g>
<g opacity="0.6">
<g filter="url(#filter_aKmUQw3vHUXkZ4_3deRLI)">
<path d="M20.5372 46.1119C24.1605 39.6117 29.1265 33.962 35.0602 29.5378C37.7627 36.2765 38.6416 49.0254 20.5372 46.1119Z"  style="mix-blend-mode:normal" fill="url(#linear_fill_aKmUQw3vHUXkZ4_3deRLI)" fill-opacity="0.9599999785423279">
</path>
</g>
</g>
<g filter="url(#filter_pbPwXFrcZkTH3dzMMMJjT)">
<path d="M21.5372 43.9153C24.024 39.4531 27.4324 35.5748 31.505 32.5378C32.0372 38.0378 28.5372 43.9153 21.5372 43.9153Z"  style="mix-blend-mode:normal" fill="url(#linear_fill_pbPwXFrcZkTH3dzMMMJjT)" >
</path>
</g>
<path fill-rule="evenodd"  fill="url(#linear_border_TQYMiQNifyf639F5t7RBa_0)" style="mix-blend-mode:normal" d="M52.5372 96.5378C72.4195 96.5378 88.5372 80.42 88.5372 60.5378C88.5372 40.6555 72.4195 24.5378 52.5372 24.5378C32.655 24.5378 16.5372 40.6555 16.5372 60.5378C16.5372 80.42 32.655 96.5378 52.5372 96.5378ZM52.5372 25.5378C71.8672 25.5378 87.5372 41.2078 87.5372 60.5378C87.5372 79.8677 71.8672 95.5378 52.5372 95.5378C33.2073 95.5378 17.5372 79.8677 17.5372 60.5378C17.5372 41.2078 33.2073 25.5378 52.5372 25.5378Z">
</path>
<path fill-rule="evenodd"  fill="url(#linear_border_TQYMiQNifyf639F5t7RBa_1)" style="mix-blend-mode:normal" d="M52.5372 96.5378C72.4195 96.5378 88.5372 80.42 88.5372 60.5378C88.5372 40.6555 72.4195 24.5378 52.5372 24.5378C32.655 24.5378 16.5372 40.6555 16.5372 60.5378C16.5372 80.42 32.655 96.5378 52.5372 96.5378ZM52.5372 25.5378C71.8672 25.5378 87.5372 41.2078 87.5372 60.5378C87.5372 79.8677 71.8672 95.5378 52.5372 95.5378C33.2073 95.5378 17.5372 79.8677 17.5372 60.5378C17.5372 41.2078 33.2073 25.5378 52.5372 25.5378Z">
</path>
<defs>
<linearGradient id="linear_fill_y8ECK7XYcghYmbJxmjsf6" x1="53.03741455078125" y1="75.03778076171875" x2="53.03741455078125" y2="117.03778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#82FFCB" stop-opacity="0" />
<stop offset="1" stop-color="#82FFCA" stop-opacity="0.68" />
</linearGradient>
<linearGradient id="linear_fill_P2rYjUvYE35dy6ADJqjN7" x1="53.53741455078125" y1="113.53778076171875" x2="53.53741455078125" y2="130.53778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#415D8F" stop-opacity="0.18" />
<stop offset="1" stop-color="#415D8F"  />
</linearGradient>
<linearGradient id="linear_border_eFWHl00shpiaBlWi1ZZej_0" x1="53.53741455078125" y1="118.53778076171875" x2="53.53741455078125" y2="142.03778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#3C5385" stop-opacity="0.15" />
<stop offset="1" stop-color="#3C5385" stop-opacity="0.15" />
</linearGradient>
<linearGradient id="linear_border_5U2bq_j6AWZJUFvUT7aG5_0" x1="53.53741455078125" y1="118.53778076171875" x2="53.53741455078125" y2="142.03778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#3C5385" stop-opacity="0.15" />
<stop offset="1" stop-color="#3C5385" stop-opacity="0.15" />
</linearGradient>
<mask id="mask-5U2bq_j6AWZJUFvUT7aG5" style="mask-type:alpha" maskUnits="userSpaceOnUse">
<path    style="mix-blend-mode:normal" fill="#2A5192" fill-opacity="0.4" d="M53.5374 142.038C77.8384 142.038 97.5374 137.561 97.5374 132.038L86.5374 118.538C86.5374 123.232 71.7624 127.038 53.5374 127.038C35.3124 127.038 20.5374 123.232 20.5374 118.538L9.53741 132.038C9.53741 137.561 29.2364 142.038 53.5374 142.038Z">
</path>
<path fill-rule="evenodd"  fill="url(#linear_border_5U2bq_j6AWZJUFvUT7aG5_0)" style="mix-blend-mode:normal" d="M96.5007 132.349Q96.3844 132.846 95.9003 133.384Q95.1456 134.222 93.6064 135.049Q90.3861 136.78 84.4287 138.134Q78.4012 139.504 70.5687 140.257Q62.4414 141.038 53.5374 141.038Q44.6334 141.038 36.5061 140.257Q28.6736 139.504 22.6462 138.134Q16.6887 136.78 13.4684 135.049Q11.9292 134.222 11.1745 133.384Q10.6904 132.846 10.5742 132.349L20.1852 120.553Q20.9484 121.688 22.6107 122.7Q25.2424 124.303 29.9536 125.516Q34.5907 126.711 40.5842 127.364Q46.7695 128.038 53.5374 128.038Q60.3054 128.038 66.4906 127.364Q72.4841 126.711 77.1212 125.516Q81.8324 124.303 84.4642 122.7Q86.1264 121.688 86.8896 120.553L96.5007 132.349ZM97.5374 132.038C97.5374 137.561 77.8384 142.038 53.5374 142.038C29.2364 142.038 9.53741 137.561 9.53741 132.038L19.6991 119.567L20.5374 118.538C20.5374 118.941 20.6467 119.339 20.8581 119.727C23.1044 123.858 36.8799 127.038 53.5374 127.038C70.195 127.038 83.9704 123.858 86.2168 119.727C86.4281 119.339 86.5374 118.941 86.5374 118.538L87.3757 119.567L97.5374 132.038Z">
</path>
</mask>
<filter id="filter_RQICWdcmn3agYRWwgcV9A" x="33.53741455078125" y="122.53778076171875" width="41" height="33" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_RQICWdcmn3agYRWwgcV9A" stdDeviation="3.5"/>
</filter>
<linearGradient id="linear_fill_Mog6b9n9t8xUBZ5IU6vuY_0" x1="53.53741455078125" y1="109.53778076171875" x2="53.53741455078125" y2="126.53778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#264B8E" stop-opacity="0" />
<stop offset="1" stop-color="#5A729E"  />
</linearGradient>
<linearGradient id="linear_fill_Mog6b9n9t8xUBZ5IU6vuY_1" x1="53.53741455078125" y1="109.53778076171875" x2="53.53741455078125" y2="126.53778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#9BB6E8" stop-opacity="0" />
<stop offset="1" stop-color="#9BB6E8"  />
</linearGradient>
<linearGradient id="linear_fill_Mog6b9n9t8xUBZ5IU6vuY_2" x1="53.53741455078125" y1="109.53778076171875" x2="53.53741455078125" y2="126.53778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0.6448222994804382" stop-color="#C6DAFF" stop-opacity="0" />
<stop offset="1" stop-color="#C6DAFF"  />
</linearGradient>
<linearGradient id="linear_border_Mog6b9n9t8xUBZ5IU6vuY_0" x1="53.53741455078125" y1="109.53778076171875" x2="53.53741455078125" y2="126.53778076171875" gradientUnits="userSpaceOnUse">
<stop offset="0.45198696851730347" stop-color="#E7E9F5" stop-opacity="0" />
<stop offset="1" stop-color="#E7E9F5"  />
</linearGradient>
<filter id="filter_HXvCmk4R_dptnSrT6O0nr" x="0" y="0" width="64.47064208984375" height="64.470703125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_HXvCmk4R_dptnSrT6O0nr" stdDeviation="7.5"/>
</filter>
<filter id="filter_ta1VTlurec0zxwrwx9pyA" x="63.70306396484375" y="61.70355224609375" width="39.5374755859375" height="39.537445068359375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_ta1VTlurec0zxwrwx9pyA" stdDeviation="5"/>
</filter>
<linearGradient id="linear_border_HMutdm6xSwV4GBfH4Xukg_0" x1="26.037233339415604" y1="36.03778093390994" x2="78.03723196188608" y2="88.03777955638041" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#62B6FC"  />
<stop offset="0.5989832878112793" stop-color="#62B6FC" stop-opacity="0" />
<stop offset="0.651858389377594" stop-color="#4D78A2" stop-opacity="0" />
<stop offset="1" stop-color="#4D78A2"  />
</linearGradient>
<linearGradient id="linear_border_HMutdm6xSwV4GBfH4Xukg_1" x1="26.037233339415604" y1="36.03778093390994" x2="78.03723196188608" y2="88.03777955638041" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#FFFFFF"  />
<stop offset="0.11100113391876221" stop-color="#FFFFFF" stop-opacity="0" />
<stop offset="0.923195481300354" stop-color="#FFFFFF" stop-opacity="0" />
<stop offset="0.9629504084587097" stop-color="#FFFFFF"  />
</linearGradient>
<mask id="mask-p4WSRw47H6JYUwF74nrdk" style="mask-type:alpha" maskUnits="userSpaceOnUse">
<circle cx="52.5372314453125" cy="60.53778076171875" r="36"  style="mix-blend-mode:normal" fill="#774C70" >
</circle>
</mask>
<filter id="filter_Nh0m_lM6pwZXS2lihwTAb" x="11.5372314453125" y="19.53778076171875" width="74" height="74" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_Nh0m_lM6pwZXS2lihwTAb" stdDeviation="3.5"/>
</filter>
<filter id="filter_43FcvB_ZQ8gthunlZmC9_" x="42.26287841796875" y="48.08197021484375" width="69.47320556640625" height="69.97250366210938" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_43FcvB_ZQ8gthunlZmC9_" stdDeviation="8"/>
</filter>
<filter id="filter_scLYxPcnWkFzFg7E9jyep" x="61.7086181640625" y="66.67507934570312" width="33.29925537109375" height="33.57286071777344" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_scLYxPcnWkFzFg7E9jyep" stdDeviation="3.5"/>
</filter>
<filter id="filter_ztRfTOGTjAUQ1o1xvP8vG" x="63.3162841796875" y="68.28768920898438" width="31.94610595703125" height="32.297698974609375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_ztRfTOGTjAUQ1o1xvP8vG" stdDeviation="3.5"/>
</filter>
<linearGradient id="linear_fill_qeIsm43CTfQOKSTSLVTaz" x1="26.536865234375" y1="37.5367431640625" x2="43.53619384765625" y2="63.53466796875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#34DCBE" stop-opacity="0.69" />
<stop offset="1" stop-color="#3CEEC3" stop-opacity="0.03" />
</linearGradient>
<filter id="filter_qeIsm43CTfQOKSTSLVTaz" x="9.5372314453125" y="17.53778076171875" width="50.23138427734375" height="50.842926025390625" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_qeIsm43CTfQOKSTSLVTaz" stdDeviation="3.5"/>
</filter>
<linearGradient id="linear_fill_aKmUQw3vHUXkZ4_3deRLI" x1="25.63671875" y1="38.278289794921875" x2="32.92169189453125" y2="44.833648681640625" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#DCECFF"  />
<stop offset="1" stop-color="#DCECFF" stop-opacity="0" />
</linearGradient>
<filter id="filter_aKmUQw3vHUXkZ4_3deRLI" x="16.5372314453125" y="25.53778076171875" width="24" height="25" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_aKmUQw3vHUXkZ4_3deRLI" stdDeviation="2"/>
</filter>
<linearGradient id="linear_fill_pbPwXFrcZkTH3dzMMMJjT" x1="24.73077392578125" y1="38.388763427734375" x2="29.29302978515625" y2="42.77703857421875" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#DFECFF"  />
<stop offset="1" stop-color="#DFECFF" stop-opacity="0" />
</linearGradient>
<filter id="filter_pbPwXFrcZkTH3dzMMMJjT" x="18.5372314453125" y="29.53778076171875" width="16.0206298828125" height="17.377471923828125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feGaussianBlur result="gaussian_blur_pbPwXFrcZkTH3dzMMMJjT" stdDeviation="1.5"/>
</filter>
<linearGradient id="linear_border_TQYMiQNifyf639F5t7RBa_0" x1="26.037233339415604" y1="36.03778093390994" x2="78.03723196188608" y2="88.03777955638041" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#70FEBA"  />
<stop offset="0.5989832878112793" stop-color="#70FEED" stop-opacity="0" />
<stop offset="0.651858389377594" stop-color="#4DA29D" stop-opacity="0" />
<stop offset="1" stop-color="#23B59A"  />
</linearGradient>
<linearGradient id="linear_border_TQYMiQNifyf639F5t7RBa_1" x1="26.037233339415604" y1="36.03778093390994" x2="78.03723196188608" y2="88.03777955638041" gradientUnits="userSpaceOnUse">
<stop offset="0" stop-color="#FFFFFF"  />
<stop offset="0.11100113391876221" stop-color="#FFFFFF" stop-opacity="0" />
<stop offset="0.923195481300354" stop-color="#FFFFFF" stop-opacity="0" />
<stop offset="0.9629504084587097" stop-color="#FFFFFF"  />
</linearGradient>
</defs>
</svg>
