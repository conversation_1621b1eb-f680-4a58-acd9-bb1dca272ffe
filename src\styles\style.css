/* 全局样式重置，防止滚动条出现 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.border-box {
  height: 100%;
  background-size: 100% 100%;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}


.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 15px;
  height: 45px;
  width: 100%;
  background-image: url("@/assets/icon/smalltitle.svg");
  background-repeat: no-repeat;
  background-position: center center;
  background-size:cover;
}

.title-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  /* padding: 0 40px; */
}

.rank-1{
  background-image: url('@/assets/icon/first_bg.svg');
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.rank-2{
  background-image: url('@/assets/icon/second_bg.svg');
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.rank-3{
  background-image: url('@/assets/icon/third_bg.svg');
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.ranking-number {
  padding:5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.ranking-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background-image: url('@/assets/icon/other_bg.svg');
}


