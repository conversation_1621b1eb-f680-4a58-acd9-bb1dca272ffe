{"name": "yl-view", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "start": "vite --mode production", "build:env": "vite build --mode development", "preview": "vite preview"}, "license": "MIT", "dependencies": {"@antv/g2plot": "^2.4.33", "@antv/l7": "^2.22.5", "@kjgl77/datav-vue3": "^1.7.4", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "screenfull": "^6.0.2", "three": "^0.177.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "vue3-count-to": "^1.1.2", "vue3-seamless-scroll": "^3.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "element-plus": "^2.9.11", "less": "^4.3.0", "pinia": "^3.0.2", "qs": "^6.14.0", "sass-embedded": "^1.89.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vite-auto-import-resolvers": "^3.2.1", "vite-plugin-pages": "^0.33.0"}}