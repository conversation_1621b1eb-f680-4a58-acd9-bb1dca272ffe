<template>
  <div class="building-vacancy-rate">
    <div class="chart-container">
      <div class="round-icon-wrapper">
        <img src="@/assets/icon/roundIcon.svg" class="round-icon-bg" alt="background" />
        <div ref="liquidfillChartRef" class="liquidfill-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts';
import 'echarts-liquidfill';

// 接收空置率数据
const props = defineProps({
  vacancyRate: {
    type: [Number, String],
    default: 12.5
  },
  showLabel: {
    type: Boolean,
    default: false
  }
});

// 图表引用
const liquidfillChartRef = ref(null);
let liquidfillChart = null;

// 初始化水球图
const initLiquidfillChart = () => {
  if (!liquidfillChartRef.value) return;

  liquidfillChart = echarts.init(liquidfillChartRef.value);

  // 创建三层水波重叠效果
  const baseValue = parseFloat(props.vacancyRate) / 100;

  const option = {
    series: [
      // 第一层水波 - 主要水位
      {
        type: 'liquidFill',
        data: [baseValue], // 主要水位
        radius: '80%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#4fc3f7' },
              { offset: 0.5, color: '#29b6f6' },
              { offset: 1, color: '#0277bd' }
            ]
          }
        ],
        backgroundStyle: {
          color: 'rgba(79, 195, 247, 0.1)',
          borderWidth: 1,
          borderColor: 'rgba(79, 195, 247, 0.3)',
          shadowColor: 'rgba(79, 195, 247, 0.3)',
          shadowBlur: 10
        },
        outline: {
          show: true,
          borderDistance: 2,
          itemStyle: {
            borderWidth: 1,
            borderColor: '#4fc3f7',
            shadowBlur: 10,
            shadowColor: 'rgba(79, 195, 247, 0.5)'
          }
        },
        label: {
          show: true,
          color: '#4fc3f7',
          fontSize: props.showLabel ? 8 : 12,
          fontWeight: 'bold',
          formatter: function(param) {
            if (props.showLabel) {
              return '空置率\n' + (param.value * 100).toFixed(1) + '%';
            }
            return (param.value * 100).toFixed(1) + '%';
          }
        },
        itemStyle: {
          opacity: 0.8,
          shadowBlur: 10,
          shadowColor: 'rgba(79, 195, 247, 0.4)'
        },
        emphasis: {
          itemStyle: {
            opacity: 1
          }
        },
        // 水波动画配置
        waveAnimation: true,
        animationDuration: 2000,
        animationDurationUpdate: 1000
      },
      // 第二层水波 - 稍低水位
      {
        type: 'liquidFill',
        data: [Math.max(0, baseValue - 0.05)], // 第二层稍低水位
        radius: '80%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(79, 195, 247, 0.6)' },
              { offset: 1, color: 'rgba(2, 119, 189, 0.6)' }
            ]
          }
        ],
        backgroundStyle: {
          color: 'transparent'
        },
        outline: {
          show: false
        },
        label: {
          show: false
        },
        itemStyle: {
          opacity: 0.6
        },
        waveAnimation: true,
        animationDuration: 2500,
        animationDurationUpdate: 1000
      },
      // 第三层水波 - 更低水位
      {
        type: 'liquidFill',
        data: [Math.max(0, baseValue - 0.1)], // 第三层更低水位
        radius: '80%',
        center: ['50%', '50%'],
        color: [
          {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(79, 195, 247, 0.4)' },
              { offset: 1, color: 'rgba(2, 119, 189, 0.4)' }
            ]
          }
        ],
        backgroundStyle: {
          color: 'transparent'
        },
        outline: {
          show: false
        },
        label: {
          show: false
        },
        itemStyle: {
          opacity: 0.4
        },
        waveAnimation: true,
        animationDuration: 3000,
        animationDurationUpdate: 1000
      }
    ]
  };

  liquidfillChart.setOption(option);
};

// 更新水球图数据
const updateChart = () => {
  if (liquidfillChart) {
    const baseValue = parseFloat(props.vacancyRate) / 100;
    const option = {
      series: [
        {
          data: [baseValue] // 第一层主要水位
        },
        {
          data: [Math.max(0, baseValue - 0.05)] // 第二层稍低水位
        },
        {
          data: [Math.max(0, baseValue - 0.1)] // 第三层更低水位
        }
      ]
    };
    liquidfillChart.setOption(option);
  }
};

// 监听空置率变化
watch(() => props.vacancyRate, () => {
  updateChart();
});

// 处理窗口大小变化
const handleResize = () => {
  if (liquidfillChart) {
    liquidfillChart.resize();
  }
};

onMounted(() => {
  nextTick(() => {
    initLiquidfillChart();
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (liquidfillChart) {
    liquidfillChart.dispose();
  }
});
</script>

<style scoped lang="less">
.building-vacancy-rate {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.chart-container {
  position: relative;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.round-icon-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.round-icon-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: contain;
}

.liquidfill-chart {
  position: relative;
  width: 80%;
  height: 80%;
  z-index: 2;
}
</style>
