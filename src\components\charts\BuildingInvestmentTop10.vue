<template>
  <div class="building-investment-top10">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="楼宇招商率TOP10" />
        
        <div class="chart-content">
          <div
            class="investment-list"
            ref="investmentListRef"
            @mouseenter="pauseScroll"
            @mouseleave="resumeScroll"
          >
            <div
              v-for="(item, index) in investmentData"
              :key="index"
              class="investment-item"
            >
               <div
                class="ranking-number"
                :class="'rank-' + (index + 1)"
              >{{ String(index + 1).padStart(2, '0') }}</div>
              <div class="building-info">
                <div class="building-name">{{ item.name }}</div>
                <div class="building-location">{{ item.location }}</div>
              </div>
              <div class="progress-section">
                <div class="progress-bar-container">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: item.rate + '%' }"
                    ></div>
                  </div>
                  <div class="rate-value">{{ item.rate }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 模拟招商率数据
const investmentData = ref([
  { name: '佳兆业广场', location: '芙蓉区', rate: 95 },
  { name: '万达广场', location: '岳麓区', rate: 92 },
  { name: '海信广场', location: '开福区', rate: 89 },
  { name: '步步高梅溪新天地', location: '岳麓区', rate: 87 },
  { name: '友谊商城', location: '芙蓉区', rate: 85 },
  { name: '华润万象城', location: '开福区', rate: 83 },
  { name: '悦方ID MALL', location: '岳麓区', rate: 81 },
  { name: '德思勤城市广场', location: '雨花区', rate: 78 },
  { name: '砂之船奥莱', location: '望城区', rate: 76 },
  { name: '长沙IFS国金中心', location: '芙蓉区', rate: 74 },
  { name: '步步高广场', location: '天心区', rate: 72 },
  { name: '平和堂', location: '芙蓉区', rate: 70 },
  { name: '王府井百货', location: '天心区', rate: 68 },
  { name: '奥克斯广场', location: '雨花区', rate: 66 },
  { name: '红星美凯龙', location: '岳麓区', rate: 64 }
]);

// 自动滚动相关
const investmentListRef = ref(null);
let scrollInterval = null;
let isScrollPaused = false;

// 开始自动滚动
const startAutoScroll = () => {
  if (scrollInterval) return;

  scrollInterval = setInterval(() => {
    if (!isScrollPaused && investmentListRef.value) {
      const container = investmentListRef.value;
      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const currentScrollTop = container.scrollTop;

      // 如果滚动到底部，回到顶部
      if (currentScrollTop + clientHeight >= scrollHeight - 20) {
        container.scrollTop = 0;
      } else {
        // 平滑滚动
        container.scrollTop += 1;
      }
    }
  }, 50); // 每50ms滚动1px
};

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 暂停滚动
const pauseScroll = () => {
  isScrollPaused = true;
};

// 恢复滚动
const resumeScroll = () => {
  isScrollPaused = false;
};

onMounted(() => {
  // 延迟启动自动滚动，确保DOM已渲染
  setTimeout(() => {
    startAutoScroll();
  }, 1000);
});

onUnmounted(() => {
  stopAutoScroll();
});
</script>

<style scoped lang="less">
.building-investment-top10 {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  padding: 15px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 15px;
  height: 40px;
  width: 100%;
}

/* 原有的标题样式已移至ChartTitle组件 */

.chart-content {
  flex: 1;
  overflow: hidden;
}

.investment-list {
  height: 100%;
  overflow-y: auto;
  padding-right: 5px;
}

.investment-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
  gap: 12px;
}

.investment-item:last-child {
  border-bottom: none;
}

.rank-number {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #4fc3f7, #81d4fa);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.building-info {
  flex: 1;
  min-width: 0;
}

.building-name {
  color: #e3f2fd;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.building-location {
  color: #81d4fa;
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
}

.progress-section {
  flex: 1;
  max-width: 120px;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(79, 195, 247, 0.2);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4fc3f7, #81d4fa);
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.rate-value {
  color: #4fc3f7;
  font-size: 12px;
  font-weight: bold;
  min-width: 35px;
  text-align: right;
}

/* 滚动条样式 */
.investment-list::-webkit-scrollbar {
  width: 4px;
}

.investment-list::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 2px;
}

.investment-list::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.5);
  border-radius: 2px;
}

.investment-list::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 195, 247, 0.7);
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .building-name {
    font-size: 12px;
  }
  
  .building-location {
    font-size: 10px;
  }
  
  .rate-value {
    font-size: 11px;
  }
}
</style>
