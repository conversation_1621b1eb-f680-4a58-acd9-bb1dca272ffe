<template>
  <div class="district-core-data">
    <div class="core-stats-grid">
      <div class="core-stat-item" v-for="stat in coreStats" :key="stat.label">
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
        <div class="stat-unit">{{ stat.unit }}</div>
        <div class="stat-icon">
          <img src="@/assets/icon/dataicon.svg" class="icon-bg" alt="data icon">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  }
});

// 区级核心数据配置
const districtCoreDataConfig = {
  '芙蓉区': {
    buildingEconomyOutput: '286.5',
    gdpTotal: '1,245.8',
    buildingCount: '168',
    companyCount: '2,845'
  },
  '天心区': {
    buildingEconomyOutput: '198.3',
    gdpTotal: '987.2',
    buildingCount: '124',
    companyCount: '1,967'
  },
  '岳麓区': {
    buildingEconomyOutput: '342.1',
    gdpTotal: '1,567.9',
    buildingCount: '203',
    companyCount: '3,421'
  },
  '开福区': {
    buildingEconomyOutput: '234.7',
    gdpTotal: '1,123.4',
    buildingCount: '156',
    companyCount: '2,234'
  },
  '雨花区': {
    buildingEconomyOutput: '267.9',
    gdpTotal: '1,334.6',
    buildingCount: '189',
    companyCount: '2,678'
  },
  '望城区': {
    buildingEconomyOutput: '145.2',
    gdpTotal: '756.3',
    buildingCount: '98',
    companyCount: '1,456'
  },
  '长沙县': {
    buildingEconomyOutput: '298.4',
    gdpTotal: '1,456.7',
    buildingCount: '234',
    companyCount: '3,123'
  }
};

// 动态核心统计数据
const coreStats = computed(() => {
  const regionName = props.selectedRegion.regionName;
  const data = districtCoreDataConfig[regionName] || districtCoreDataConfig['芙蓉区'];
  
  return [
    { 
      label: '楼宇经济年度产值', 
      value: data.buildingEconomyOutput, 
      unit: '亿元' 
    },
    { 
      label: 'GDP总额', 
      value: data.gdpTotal, 
      unit: '亿元' 
    },
    { 
      label: '楼宇数量', 
      value: data.buildingCount, 
      unit: '栋' 
    },
    { 
      label: '企业数量', 
      value: data.companyCount, 
      unit: '家' 
    }
  ];
});

// 模拟数据更新
const updateCoreData = () => {
  // 这里可以添加数据更新逻辑
};

let updateInterval = null;

onMounted(() => {
  // 数据更新逻辑
  updateInterval = setInterval(() => {
    updateCoreData();
  }, 10000); // 每10秒更新一次数据
});

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval);
  }
});
</script>

<style scoped lang="less">
.district-core-data {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10px 0;
}

.core-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
}

.core-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  min-width: 140px;
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 0 20px rgba(79, 195, 247, 0.8);
  margin-bottom: 8px;
  z-index: 2;
  position: relative;
  animation: valueGlow 2s ease-in-out infinite alternate;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

@keyframes valueGlow {
  0% {
    text-shadow: 0 0 20px rgba(79, 195, 247, 0.8);
  }
  100% {
    text-shadow: 0 0 30px rgba(79, 195, 247, 1), 0 0 40px rgba(129, 212, 250, 0.6);
  }
}

.stat-label {
  color: #e3f2fd;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.5px;
  z-index: 2;
  position: absolute;
  top: 90px;
  line-height: 1.2;
}

.stat-unit {
  color: #4fc3f7;
  font-size: 11px;
  opacity: 0.8;
  z-index: 2;
  position: absolute;
  top: 105px;
}

.stat-icon {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.icon-bg {
  width: 120px;
  height: 120px;
  animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .core-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .stat-value {
    font-size: 28px;
  }
}
</style>
