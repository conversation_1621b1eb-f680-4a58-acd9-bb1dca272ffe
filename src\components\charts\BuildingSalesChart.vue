<template>
  <div class="building-sales-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="楼宇销售" />
        <div ref="salesChartRef" class="sales-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 图表引用
const salesChartRef = ref(null);
let salesChart = null;

// 楼宇销售数据配置
const salesData = ref({
  quarters: [
    '2020Q1', '2020Q2', '2020Q3', '2020Q4',
    '2021Q1', '2021Q2', '2021Q3', '2021Q4',
    '2022Q1', '2022Q2', '2022Q3', '2022Q4',
    '2023Q1', '2023Q2', '2023Q3', '2023Q4',
    '2024Q1', '2024Q2', '2024Q3', '2024Q4'
  ],
  salesArea: [
    27099, 49897, 26606, 58129,  // 2020年
    13334, 41870, 40593, 34976,  // 2021年
    12002, 24849, 5931, 2113,    // 2022年
    7367, 4102, 3249, 11108,     // 2023年
    11770, 2076, 1453, 2461      // 2024年
  ], // 销售面积(㎡)
  salesPrice: [
    14671, 12734, 15841, 13720,  // 2020年
    14609, 13662, 12729, 15801,  // 2021年
    14452, 12496, 13099, 13881,  // 2022年
    13394, 12214, 14047, 13433,  // 2023年
    12450, 12045, 10051, 10746   // 2024年
  ] // 销售均价(元/㎡)
});

// 初始化销售图表
const initSalesChart = () => {
  if (!salesChartRef.value) return;

  salesChart = echarts.init(salesChartRef.value);

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(0, 20, 50, 0.8)',
      borderColor: '#4fc3f7',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          if (param.seriesName === '销售面积') {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}㎡</div>`;
          } else {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}元/㎡</div>`;
          }
        });
        return result;
      }
    },
    legend: {
      data: ['销售面积', '销售均价'],
      textStyle: {
        color: '#e3f2fd',
        fontSize: 14
      },
      top: 10,
      itemWidth: 12,
      itemHeight: 8
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: salesData.value.quarters,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#e3f2fd',
          fontSize: 13,
          rotate: 45
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(79, 195, 247, 0.3)'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '销售面积(㎡)',
        position: 'left',
        nameTextStyle: {
          color: '#e3f2fd',
          fontSize: 14
        },
        axisLabel: {
          color: '#e3f2fd',
          fontSize: 13,
          formatter: function(value) {
            if (value >= 10000) {
              return (value / 10000).toFixed(1) + '万';
            }
            return value;
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(79, 195, 247, 0.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(79, 195, 247, 0.1)'
          }
        }
      },
      {
        type: 'value',
        name: '销售均价(元/㎡)',
        position: 'right',
        nameTextStyle: {
          color: '#e3f2fd',
          fontSize: 14
        },
        axisLabel: {
          color: '#e3f2fd',
          fontSize: 13,
          formatter: function(value) {
            return value;
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(79, 195, 247, 0.3)'
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '销售面积',
        type: 'bar',
        yAxisIndex: 0,
        data: salesData.value.salesArea,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#4fc3f7' },
            { offset: 1, color: 'rgba(79, 195, 247, 0.3)' }
          ])
        },
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: '销售均价',
        type: 'line',
        yAxisIndex: 1,
        data: salesData.value.salesPrice,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: '#ff7043'
        },
        itemStyle: {
          color: '#ff7043'
        },
        areaStyle: {
          opacity: 0.1,
          color: '#ff7043'
        },
        emphasis: {
          focus: 'series'
        }
      }
    ]
  };

  salesChart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (salesChart) {
    salesChart.resize();
  }
};

// 数据更新函数（使用固定真实数据）
const updateData = () => {
  // 使用固定的真实销售数据，无需随机更新
  console.log('楼宇销售数据已加载:', salesData.value);
};

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initSalesChart();
    updateData(); // 初始化时调用一次数据更新
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (salesChart) {
    salesChart.dispose();
  }
});
</script>

<style scoped lang="less">
.building-sales-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  padding: 5px 15px;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.sales-chart {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
