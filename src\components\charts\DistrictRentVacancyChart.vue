<template>
  <div class="district-rent-vacancy-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="区域租赁均价及空置率" />
        <div ref="rentVacancyChartRef" class="rent-vacancy-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from "vue";
import * as echarts from "echarts";
import ChartTitle from '@/components/common/ChartTitle.vue';

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: "长沙市",
      level: "city",
      areaData: null
    })
  }
});

// 图表引用
const rentVacancyChartRef = ref(null);
let rentVacancyChart = null;

// 区域租赁均价及空置率数据配置（使用真实JSON数据）
const districtRentVacancyData = {
  芙蓉区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [62.3, 62.4],
        vacancyRates: [1.5, 0.9],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [63.9, 61.9],
        vacancyRates: [4.5, 4.2],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [95.5, 76],
        vacancyRates: [35.9, 37],
        color: "#66bb6a"
      }
    }
  },
  开福区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [68.5, 63],
        vacancyRates: [12.3, 21],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [67.9, 64.1],
        vacancyRates: [22.9, 28.5],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [81.1, 76],
        vacancyRates: [27.4, 22.8],
        color: "#66bb6a"
      }
    }
  },
  岳麓区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [60.2, 54.5],
        vacancyRates: [10.9, 6.9],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [74, 76.3],
        vacancyRates: [8.7, 21],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [102.9, 92.1],
        vacancyRates: [35.5, 33.7],
        color: "#66bb6a"
      }
    }
  },
  天心区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [60.4, 52.8],
        vacancyRates: [7, 18.3],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [85, 76.8],
        vacancyRates: [21.4, 18.8],
        color: "#ffa726"
      }
    }
  },
  望城区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [40, 40],
        vacancyRates: [29.1, 29.1],
        color: "#ff7043"
      }
    }
  },
  雨花区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [63.6, 57.2],
        vacancyRates: [18.2, 20.7],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [76, 74.7],
        vacancyRates: [16.4, 15.2],
        color: "#ffa726"
      }
    }
  },
  长沙县: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [50.6, 46.6],
        vacancyRates: [17.1, 15.3],
        color: "#ff7043"
      }
    }
  }
};

// 动态数据
const chartData = computed(() => {
  const regionName = props.selectedRegion.regionName;
  return (
    districtRentVacancyData[regionName] || districtRentVacancyData["芙蓉区"]
  );
});

// 初始化图表
const initRentVacancyChart = () => {
  if (!rentVacancyChartRef.value) return;

  rentVacancyChart = echarts.init(rentVacancyChartRef.value);
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!rentVacancyChart) return;

  const currentData = chartData.value;
  const starLevels = Object.keys(currentData.series);

  // 动态生成系列数据
  const series = [];

  // 分别收集租金和空置率的图例数据
  const rentLegendData = [];
  const vacancyLegendData = [];

  // 为每个星级生成租金柱状图和空置率折线图
  starLevels.forEach((level) => {
    const levelData = currentData.series[level];

    // 只有当该星级有数据时才添加到图表中
    if (levelData && levelData.rentPrices && levelData.vacancyRates) {
      // 租金柱状图
      series.push({
        name: `${level}租金`,
        type: "bar",
        yAxisIndex: 0,
        data: levelData.rentPrices,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: levelData.color },
            { offset: 1, color: levelData.color + "4D" }
          ])
        },
        emphasis: {
          focus: "series"
        }
      });

      // 空置率折线图
      series.push({
        name: `${level}空置率`,
        type: "line",
        yAxisIndex: 1,
        data: levelData.vacancyRates,
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: levelData.color,
          type: "dashed"
        },
        itemStyle: {
          color: levelData.color
        },
        emphasis: {
          focus: "series"
        }
      });

      rentLegendData.push(`${level}租金`);
      vacancyLegendData.push(`${level}空置率`);
    }
  });

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999"
        }
      },
      backgroundColor: "rgba(0, 20, 50, 0.8)",
      borderColor: "#4fc3f7",
      textStyle: { color: "#fff" },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          if (param.seriesName.includes("租金")) {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}元/月/㎡</div>`;
          } else {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}%</div>`;
          }
        });
        return result;
      }
    },
    legend: [
      {
        // 第一行：租金图例
        data: rentLegendData,
        textStyle: {
          color: "#e3f2fd",
          fontSize: 10
        },
        bottom: 35,
        left: "center",
        itemWidth: 10,
        itemHeight: 6,
        itemGap: 15,
        orient: "horizontal"
      },
      {
        // 第二行：空置率图例
        data: vacancyLegendData,
        textStyle: {
          color: "#e3f2fd",
          fontSize: 10
        },
        bottom: 15,
        left: "center",
        itemWidth: 10,
        itemHeight: 6,
        itemGap: 15,
        orient: "horizontal"
      }
    ],
    grid: {
      left: "10%",
      right: "10%",
      bottom: "25%", // 增加底部空间给两行图例
      top: "15%", // 减少顶部空间，因为图例移到了底部
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: currentData.categories,
        axisPointer: {
          type: "shadow"
        },
        axisLabel: {
          color: "#e3f2fd",
          fontSize: 11
        },
        axisLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.3)"
          }
        }
      }
    ],
    yAxis: [
      {
        type: "value",
        name: "租赁均价(元/月/㎡)",
        position: "left",
        nameTextStyle: {
          color: "#e3f2fd",
          fontSize: 11
        },
        axisLabel: {
          color: "#e3f2fd",
          fontSize: 11,
          formatter: "{value}"
        },
        axisLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.3)"
          }
        },
        splitLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.1)"
          }
        }
      },
      {
        type: "value",
        name: "空置率(%)",
        position: "right",
        nameTextStyle: {
          color: "#e3f2fd",
          fontSize: 11
        },
        axisLabel: {
          color: "#e3f2fd",
          fontSize: 11,
          formatter: "{value}%"
        },
        axisLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.3)"
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: series
  };

  rentVacancyChart.setOption(option);
};



// 处理窗口大小变化
const handleResize = () => {
  if (rentVacancyChart) {
    rentVacancyChart.resize();
  }
};

// 监听选中区域变化
watch(
  () => props.selectedRegion,
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    initRentVacancyChart();
  });

  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (rentVacancyChart) {
    rentVacancyChart.dispose();
  }
});
</script>

<style scoped lang="less">
.district-rent-vacancy-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 10px 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.section-title {
  position: relative;
  z-index: 1;
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
  margin-left: 40px;
  text-align: left;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  white-space: nowrap;
}

.rent-vacancy-chart {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
